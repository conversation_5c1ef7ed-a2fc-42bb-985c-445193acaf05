import { useState, useEffect, useCallback, useRef } from "react";

// Simple in-memory cache
const cache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Custom hook for API calls with caching
 * @param {string} key - Cache key
 * @param {Function} apiCall - Function that returns a promise
 * @param {Object} options - Configuration options
 */
export const useApiCache = (key, apiCall, options = {}) => {
  const {
    enabled = true,
    cacheDuration = CACHE_DURATION,
    dependencies = [],
    onSuccess,
    onError
  } = options;

  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const abortControllerRef = useRef(null);

  const getCachedData = useCallback((cacheKey) => {
    const cached = cache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < cacheDuration) {
      return cached.data;
    }
    return null;
  }, [cacheDuration]);

  const setCachedData = useCallback((cacheKey, data) => {
    cache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
  }, []);

  const fetchData = useCallback(async (forceRefresh = false) => {
    if (!enabled) return;

    // Check cache first
    if (!forceRefresh) {
      const cachedData = getCachedData(key);
      if (cachedData) {
        setData(cachedData);
        setLoading(false);
        setError(null);
        return cachedData;
      }
    }

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();
    setLoading(true);
    setError(null);

    try {
      const result = await apiCall(abortControllerRef.current.signal);
      
      if (!abortControllerRef.current.signal.aborted) {
        setData(result);
        setCachedData(key, result);
        onSuccess?.(result);
      }
      
      return result;
    } catch (err) {
      if (!abortControllerRef.current.signal.aborted) {
        setError(err);
        onError?.(err);
      }
      throw err;
    } finally {
      if (!abortControllerRef.current.signal.aborted) {
        setLoading(false);
      }
    }
  }, [key, apiCall, enabled, getCachedData, setCachedData, onSuccess, onError]);

  const refresh = useCallback(() => {
    return fetchData(true);
  }, [fetchData]);

  const clearCache = useCallback(() => {
    cache.delete(key);
  }, [key]);

  // Auto-fetch on mount and dependency changes
  useEffect(() => {
    fetchData();
    
    // Cleanup on unmount
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [fetchData, ...dependencies]);

  return {
    data,
    loading,
    error,
    refresh,
    clearCache,
    fetchData
  };
};

// Hook for clearing all cache
export const useClearAllCache = () => {
  return useCallback(() => {
    cache.clear();
  }, []);
};

// Hook for cache statistics
export const useCacheStats = () => {
  return useCallback(() => {
    return {
      size: cache.size,
      keys: Array.from(cache.keys())
    };
  }, []);
};
