// Health Check and Monitoring Utilities
import { loggerInfo, loggerError } from './helper.js';

// Health check configuration
const HEALTH_CHECK_CONFIG = {
  timeout: 5000, // 5 seconds
  retries: 3,
  interval: 30000, // 30 seconds
  endpoints: {
    database: '/api/health/database',
    proactiveAI: '/api/health/proactive-ai',
    shopify: '/api/health/shopify',
    billing: '/api/health/billing'
  }
};

// Health status types
export const HEALTH_STATUS = {
  HEALTHY: 'healthy',
  DEGRADED: 'degraded',
  UNHEALTHY: 'unhealthy',
  UNKNOWN: 'unknown'
};

// System health checker class
export class HealthChecker {
  constructor(config = {}) {
    this.config = { ...HEALTH_CHECK_CONFIG, ...config };
    this.status = HEALTH_STATUS.UNKNOWN;
    this.lastCheck = null;
    this.checks = new Map();
    this.listeners = new Set();
    this.intervalId = null;
  }

  // Add health check listener
  addListener(callback) {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  // Notify all listeners of status change
  notifyListeners(status, checks) {
    this.listeners.forEach(callback => {
      try {
        callback(status, checks);
      } catch (error) {
        loggerError('Health check listener error', '', error.message);
      }
    });
  }

  // Perform individual health check
  async performCheck(name, url, options = {}) {
    const startTime = Date.now();
    const check = {
      name,
      status: HEALTH_STATUS.UNKNOWN,
      responseTime: 0,
      error: null,
      timestamp: new Date().toISOString()
    };

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

      const response = await fetch(url, {
        method: 'GET',
        signal: controller.signal,
        ...options
      });

      clearTimeout(timeoutId);
      check.responseTime = Date.now() - startTime;

      if (response.ok) {
        const data = await response.json();
        check.status = data.status || HEALTH_STATUS.HEALTHY;
        check.details = data.details;
      } else {
        check.status = HEALTH_STATUS.UNHEALTHY;
        check.error = `HTTP ${response.status}: ${response.statusText}`;
      }
    } catch (error) {
      check.responseTime = Date.now() - startTime;
      check.status = HEALTH_STATUS.UNHEALTHY;
      check.error = error.message;
      
      if (error.name === 'AbortError') {
        check.error = 'Request timeout';
      }
    }

    return check;
  }

  // Perform all health checks
  async performAllChecks() {
    const checks = new Map();
    const promises = Object.entries(this.config.endpoints).map(async ([name, url]) => {
      const check = await this.performCheck(name, url);
      checks.set(name, check);
      return check;
    });

    await Promise.all(promises);
    
    // Determine overall system status
    const statuses = Array.from(checks.values()).map(check => check.status);
    let overallStatus = HEALTH_STATUS.HEALTHY;

    if (statuses.includes(HEALTH_STATUS.UNHEALTHY)) {
      overallStatus = HEALTH_STATUS.UNHEALTHY;
    } else if (statuses.includes(HEALTH_STATUS.DEGRADED)) {
      overallStatus = HEALTH_STATUS.DEGRADED;
    }

    this.status = overallStatus;
    this.checks = checks;
    this.lastCheck = new Date();

    loggerInfo('Health check completed', '', {
      status: overallStatus,
      checks: Object.fromEntries(checks),
      timestamp: this.lastCheck.toISOString()
    });

    this.notifyListeners(overallStatus, checks);
    return { status: overallStatus, checks, timestamp: this.lastCheck };
  }

  // Start continuous health monitoring
  startMonitoring() {
    if (this.intervalId) {
      this.stopMonitoring();
    }

    // Perform initial check
    this.performAllChecks();

    // Set up interval for continuous monitoring
    this.intervalId = setInterval(() => {
      this.performAllChecks();
    }, this.config.interval);

    loggerInfo('Health monitoring started', '', {
      interval: this.config.interval
    });
  }

  // Stop health monitoring
  stopMonitoring() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      loggerInfo('Health monitoring stopped');
    }
  }

  // Get current health status
  getStatus() {
    return {
      status: this.status,
      checks: Object.fromEntries(this.checks),
      lastCheck: this.lastCheck,
      isMonitoring: !!this.intervalId
    };
  }
}

// Performance monitoring utilities
export class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.observers = new Map();
  }

  // Start timing a operation
  startTiming(name) {
    const startTime = performance.now();
    return {
      end: () => {
        const duration = performance.now() - startTime;
        this.recordMetric(name, duration);
        return duration;
      }
    };
  }

  // Record a metric
  recordMetric(name, value, unit = 'ms') {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const metrics = this.metrics.get(name);
    metrics.push({
      value,
      unit,
      timestamp: Date.now()
    });

    // Keep only last 100 measurements
    if (metrics.length > 100) {
      metrics.shift();
    }
  }

  // Get performance statistics
  getStats(name) {
    const metrics = this.metrics.get(name);
    if (!metrics || metrics.length === 0) {
      return null;
    }

    const values = metrics.map(m => m.value);
    const sum = values.reduce((a, b) => a + b, 0);
    const avg = sum / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);
    
    // Calculate percentiles
    const sorted = [...values].sort((a, b) => a - b);
    const p50 = sorted[Math.floor(sorted.length * 0.5)];
    const p95 = sorted[Math.floor(sorted.length * 0.95)];
    const p99 = sorted[Math.floor(sorted.length * 0.99)];

    return {
      count: values.length,
      avg: Math.round(avg * 100) / 100,
      min: Math.round(min * 100) / 100,
      max: Math.round(max * 100) / 100,
      p50: Math.round(p50 * 100) / 100,
      p95: Math.round(p95 * 100) / 100,
      p99: Math.round(p99 * 100) / 100,
      unit: metrics[0].unit
    };
  }

  // Monitor Core Web Vitals
  monitorWebVitals() {
    if (typeof window === 'undefined') return;

    // Largest Contentful Paint (LCP)
    if ('PerformanceObserver' in window) {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.recordMetric('LCP', lastEntry.startTime);
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.set('lcp', lcpObserver);

      // First Input Delay (FID)
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          this.recordMetric('FID', entry.processingStart - entry.startTime);
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });
      this.observers.set('fid', fidObserver);

      // Cumulative Layout Shift (CLS)
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        this.recordMetric('CLS', clsValue);
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
      this.observers.set('cls', clsObserver);
    }
  }

  // Stop monitoring
  stopMonitoring() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
  }

  // Get all metrics
  getAllMetrics() {
    const result = {};
    this.metrics.forEach((values, name) => {
      result[name] = this.getStats(name);
    });
    return result;
  }
}

// Error tracking utilities
export class ErrorTracker {
  constructor() {
    this.errors = [];
    this.maxErrors = 100;
  }

  // Track an error
  trackError(error, context = {}) {
    const errorEntry = {
      id: Date.now() + Math.random(),
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      context,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
      url: typeof window !== 'undefined' ? window.location.href : 'unknown'
    };

    this.errors.unshift(errorEntry);
    
    // Keep only recent errors
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(0, this.maxErrors);
    }

    loggerError('Error tracked', context.shopName || '', error.message, errorEntry);
    return errorEntry.id;
  }

  // Get error statistics
  getErrorStats() {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    const oneDay = 24 * oneHour;

    const recentErrors = this.errors.filter(error => 
      now - new Date(error.timestamp).getTime() < oneHour
    );

    const dailyErrors = this.errors.filter(error => 
      now - new Date(error.timestamp).getTime() < oneDay
    );

    return {
      total: this.errors.length,
      lastHour: recentErrors.length,
      lastDay: dailyErrors.length,
      mostRecent: this.errors[0] || null
    };
  }

  // Get errors by type
  getErrorsByType() {
    const errorTypes = {};
    this.errors.forEach(error => {
      const type = error.message.split(':')[0] || 'Unknown';
      errorTypes[type] = (errorTypes[type] || 0) + 1;
    });
    return errorTypes;
  }
}

// Global instances
export const healthChecker = new HealthChecker();
export const performanceMonitor = new PerformanceMonitor();
export const errorTracker = new ErrorTracker();

// Initialize monitoring
export const initializeMonitoring = (config = {}) => {
  if (typeof window !== 'undefined') {
    performanceMonitor.monitorWebVitals();
    
    // Track unhandled errors
    window.addEventListener('error', (event) => {
      errorTracker.trackError(event.error, {
        type: 'unhandled_error',
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      });
    });

    // Track unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      errorTracker.trackError(new Error(event.reason), {
        type: 'unhandled_promise_rejection'
      });
    });
  }

  if (config.enableHealthChecks !== false) {
    healthChecker.startMonitoring();
  }

  loggerInfo('Monitoring initialized', '', config);
};
