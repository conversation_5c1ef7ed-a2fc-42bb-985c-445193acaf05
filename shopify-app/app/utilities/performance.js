/**
 * Performance monitoring utilities
 */

// Performance timing tracker
class PerformanceTracker {
  constructor() {
    this.timings = new Map();
  }

  start(label) {
    this.timings.set(label, performance.now());
  }

  end(label) {
    const startTime = this.timings.get(label);
    if (startTime) {
      const duration = performance.now() - startTime;
      this.timings.delete(label);
      
      // Log performance in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`⏱️ ${label}: ${duration.toFixed(2)}ms`);
      }
      
      return duration;
    }
    return null;
  }

  measure(label, fn) {
    this.start(label);
    const result = fn();
    
    if (result instanceof Promise) {
      return result.finally(() => this.end(label));
    } else {
      this.end(label);
      return result;
    }
  }
}

export const performanceTracker = new PerformanceTracker();

// React hook for performance tracking
export const usePerformanceTracker = () => {
  return {
    start: performanceTracker.start.bind(performanceTracker),
    end: performanceTracker.end.bind(performanceTracker),
    measure: performanceTracker.measure.bind(performanceTracker)
  };
};

// Bundle size analyzer (development only)
export const analyzeBundleSize = () => {
  if (process.env.NODE_ENV !== 'development') return;
  
  const scripts = document.querySelectorAll('script[src]');
  const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
  
  console.group('📦 Bundle Analysis');
  console.log(`Scripts: ${scripts.length}`);
  console.log(`Stylesheets: ${stylesheets.length}`);
  
  scripts.forEach((script, index) => {
    console.log(`Script ${index + 1}: ${script.src}`);
  });
  
  stylesheets.forEach((stylesheet, index) => {
    console.log(`Stylesheet ${index + 1}: ${stylesheet.href}`);
  });
  
  console.groupEnd();
};

// Memory usage tracker
export const trackMemoryUsage = () => {
  if (!performance.memory) return null;
  
  const memory = {
    used: Math.round(performance.memory.usedJSHeapSize / 1048576),
    total: Math.round(performance.memory.totalJSHeapSize / 1048576),
    limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576)
  };
  
  if (process.env.NODE_ENV === 'development') {
    console.log(`🧠 Memory: ${memory.used}MB / ${memory.total}MB (limit: ${memory.limit}MB)`);
  }
  
  return memory;
};

// Network performance tracker
export const trackNetworkPerformance = () => {
  if (!navigator.connection) return null;
  
  const connection = {
    effectiveType: navigator.connection.effectiveType,
    downlink: navigator.connection.downlink,
    rtt: navigator.connection.rtt,
    saveData: navigator.connection.saveData
  };
  
  if (process.env.NODE_ENV === 'development') {
    console.log('🌐 Network:', connection);
  }
  
  return connection;
};

// Page load performance
export const trackPageLoad = () => {
  if (typeof window === 'undefined') return;
  
  window.addEventListener('load', () => {
    setTimeout(() => {
      const navigation = performance.getEntriesByType('navigation')[0];
      
      if (navigation) {
        const metrics = {
          dns: navigation.domainLookupEnd - navigation.domainLookupStart,
          tcp: navigation.connectEnd - navigation.connectStart,
          request: navigation.responseStart - navigation.requestStart,
          response: navigation.responseEnd - navigation.responseStart,
          dom: navigation.domContentLoadedEventEnd - navigation.navigationStart,
          load: navigation.loadEventEnd - navigation.navigationStart
        };
        
        if (process.env.NODE_ENV === 'development') {
          console.group('📊 Page Load Metrics');
          Object.entries(metrics).forEach(([key, value]) => {
            console.log(`${key}: ${value.toFixed(2)}ms`);
          });
          console.groupEnd();
        }
        
        return metrics;
      }
    }, 0);
  });
};

// Initialize performance tracking
export const initPerformanceTracking = () => {
  if (typeof window === 'undefined') return;
  
  trackPageLoad();
  
  // Track memory usage periodically in development
  if (process.env.NODE_ENV === 'development') {
    setInterval(() => {
      trackMemoryUsage();
    }, 30000); // Every 30 seconds
  }
};
