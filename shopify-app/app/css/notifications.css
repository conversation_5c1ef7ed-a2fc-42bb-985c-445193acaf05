/* Notification System Styles */

/* === NOTIFICATION CONTAINER === */
.notification-container {
  position: fixed;
  top: var(--space-xl);
  right: var(--space-xl);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  max-width: 400px;
  pointer-events: none;
}

.notification-container > * {
  pointer-events: auto;
}

.notification-item {
  animation: slideInRight 0.3s ease-out;
  margin-bottom: var(--space-sm);
}

/* === ENHANCED TOAST === */
.enhanced-toast {
  display: flex;
  align-items: flex-start;
  gap: var(--space-md);
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  animation: slideInRight 0.3s ease-out;
  position: relative;
  min-width: 300px;
}

.enhanced-toast--success {
  border-left: 4px solid var(--color-success-500);
  background: var(--color-success-50);
}

.enhanced-toast--error {
  border-left: 4px solid var(--color-error-500);
  background: var(--color-error-50);
}

.enhanced-toast--warning {
  border-left: 4px solid var(--color-warning-500);
  background: var(--color-warning-50);
}

.enhanced-toast--info {
  border-left: 4px solid var(--color-primary-500);
  background: var(--color-primary-50);
}

.enhanced-toast__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.enhanced-toast__message {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
}

.enhanced-toast__action {
  margin-top: var(--space-xs);
}

.enhanced-toast__close {
  position: absolute;
  top: var(--space-sm);
  right: var(--space-sm);
  background: none;
  border: none;
  font-size: var(--font-size-lg);
  color: var(--color-text-tertiary);
  cursor: pointer;
  padding: var(--space-xs);
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
}

.enhanced-toast__close:hover {
  background: var(--color-background-secondary);
  color: var(--color-text-primary);
}

/* === NOTIFICATION BANNER === */
.notification-banner {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
  padding: var(--space-lg) var(--space-xl);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-md);
  position: relative;
}

.notification-banner--success {
  background: var(--color-success-100);
  border: 1px solid var(--color-success-200);
  color: var(--color-success-800);
}

.notification-banner--error {
  background: var(--color-error-100);
  border: 1px solid var(--color-error-200);
  color: var(--color-error-800);
}

.notification-banner--warning {
  background: var(--color-warning-100);
  border: 1px solid var(--color-warning-200);
  color: var(--color-warning-800);
}

.notification-banner--info {
  background: var(--color-primary-100);
  border: 1px solid var(--color-primary-200);
  color: var(--color-primary-800);
}

.notification-banner__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.notification-banner__title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
}

.notification-banner__message {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  opacity: 0.9;
}

.notification-banner__action {
  margin-top: var(--space-sm);
}

.notification-banner__close {
  position: absolute;
  top: var(--space-md);
  right: var(--space-md);
  background: none;
  border: none;
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--space-xs);
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
  opacity: 0.7;
}

.notification-banner__close:hover {
  opacity: 1;
  background: rgba(0, 0, 0, 0.1);
}

/* === PROGRESS NOTIFICATION === */
.progress-notification {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-md);
  animation: slideInRight 0.3s ease-out;
}

.progress-notification__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.progress-notification__title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.progress-notification__cancel {
  background: none;
  border: 1px solid var(--color-border);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: var(--transition-fast);
  color: var(--color-text-secondary);
}

.progress-notification__cancel:hover {
  background: var(--color-background-secondary);
  border-color: var(--color-border-hover);
}

.progress-notification__progress {
  width: 100%;
  height: 8px;
  background: var(--color-background-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--space-sm);
}

.progress-notification__bar {
  height: 100%;
  background: var(--color-primary-500);
  border-radius: var(--radius-full);
  transition: width 0.3s ease-out;
}

.progress-notification__message {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
}

/* === ANIMATIONS === */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
  .notification-container {
    top: var(--space-md);
    right: var(--space-md);
    left: var(--space-md);
    max-width: none;
  }

  .enhanced-toast {
    min-width: auto;
    padding: var(--space-md);
  }

  .notification-banner {
    padding: var(--space-md);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-md);
  }

  .notification-banner__close {
    position: static;
    align-self: flex-end;
  }
}

/* === ACCESSIBILITY === */
@media (prefers-reduced-motion: reduce) {
  .enhanced-toast,
  .progress-notification {
    animation: none;
  }

  .progress-notification__bar {
    transition: none;
  }
}

/* === SIMPLE NOTIFICATIONS === */
.simple-notification-container {
  position: fixed;
  top: var(--space-xl);
  right: var(--space-xl);
  z-index: 9999;
  max-width: 400px;
  pointer-events: none;
}

.simple-notification-container > * {
  pointer-events: auto;
}

.simple-notification-item {
  animation: slideInRight 0.3s ease-out;
  margin-bottom: var(--space-sm);
}

.inline-notification {
  margin-bottom: var(--space-md);
}

.status-notification {
  margin-bottom: var(--space-md);
}

.status-notification-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.status-notification-progress {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.progress-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--color-background-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--color-primary-500);
  border-radius: var(--radius-full);
  transition: width 0.3s ease-out;
}

/* === DARK MODE PREPARATION === */
@media (prefers-color-scheme: dark) {
  .enhanced-toast {
    background: var(--color-surface-dark, #1a1a1a);
    border-color: var(--color-border-dark, #333);
  }

  .notification-banner {
    /* Dark mode colors will be defined when implementing dark theme */
  }
}
