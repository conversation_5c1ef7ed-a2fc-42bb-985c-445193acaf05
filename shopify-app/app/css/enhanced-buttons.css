/* Enhanced <PERSON><PERSON> Styles */

/* === BASE BUTTON STYLES === */
.enhanced-button {
  position: relative;
  display: inline-block;
}

.enhanced-button button[class*="Polaris-Button"] {
  transition: var(--transition-fast);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  position: relative;
  overflow: hidden;
}

/* === BUTTON VARIANTS === */
.enhanced-button--primary button[class*="Polaris-Button"] {
  background: var(--color-primary-600);
  border-color: var(--color-primary-600);
  color: white;
  box-shadow: var(--shadow-sm);
}

.enhanced-button--primary:hover button[class*="Polaris-Button"]:not(:disabled) {
  background: var(--color-primary-700);
  border-color: var(--color-primary-700);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.enhanced-button--secondary button[class*="Polaris-Button"] {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text-primary);
  box-shadow: var(--shadow-xs);
}

.enhanced-button--secondary:hover button[class*="Polaris-Button"]:not(:disabled) {
  background: var(--color-surface-hover);
  border-color: var(--color-border-hover);
  box-shadow: var(--shadow-sm);
  transform: translateY(-1px);
}

.enhanced-button--tertiary button[class*="Polaris-Button"] {
  background: transparent;
  border: none;
  color: var(--color-primary-600);
  box-shadow: none;
}

.enhanced-button--tertiary:hover button[class*="Polaris-Button"]:not(:disabled) {
  background: var(--color-primary-50);
  color: var(--color-primary-700);
}

.enhanced-button--destructive button[class*="Polaris-Button"] {
  background: var(--color-error-600);
  border-color: var(--color-error-600);
  color: white;
  box-shadow: var(--shadow-sm);
}

.enhanced-button--destructive:hover button[class*="Polaris-Button"]:not(:disabled) {
  background: var(--color-error-700);
  border-color: var(--color-error-700);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.enhanced-button--success button[class*="Polaris-Button"] {
  background: var(--color-success-600);
  border-color: var(--color-success-600);
  color: white;
  box-shadow: var(--shadow-sm);
}

.enhanced-button--success:hover button[class*="Polaris-Button"]:not(:disabled) {
  background: var(--color-success-700);
  border-color: var(--color-success-700);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.enhanced-button--warning button[class*="Polaris-Button"] {
  background: var(--color-warning-600);
  border-color: var(--color-warning-600);
  color: white;
  box-shadow: var(--shadow-sm);
}

.enhanced-button--warning:hover button[class*="Polaris-Button"]:not(:disabled) {
  background: var(--color-warning-700);
  border-color: var(--color-warning-700);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* === BUTTON SIZES === */
.enhanced-button--small button[class*="Polaris-Button"] {
  padding: var(--space-xs) var(--space-md);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.enhanced-button--medium button[class*="Polaris-Button"] {
  padding: var(--space-sm) var(--space-lg);
  font-size: var(--font-size-base);
}

.enhanced-button--large button[class*="Polaris-Button"] {
  padding: var(--space-md) var(--space-xl);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-lg);
}

/* === BUTTON STATES === */
.enhanced-button--loading button[class*="Polaris-Button"] {
  pointer-events: none;
  opacity: 0.8;
}

.enhanced-button--disabled button[class*="Polaris-Button"] {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.enhanced-button--full-width {
  width: 100%;
}

.enhanced-button--full-width button[class*="Polaris-Button"] {
  width: 100%;
}

/* === BUTTON ICONS === */
.enhanced-button__icon {
  display: inline-flex;
  align-items: center;
}

.enhanced-button__icon--left {
  margin-right: var(--space-xs);
}

.enhanced-button__icon--right {
  margin-left: var(--space-xs);
}

.enhanced-button__text {
  display: inline-flex;
  align-items: center;
}

/* === ICON BUTTON STYLES === */
.icon-button {
  position: relative;
  display: inline-block;
}

.icon-button button[class*="Polaris-Button"] {
  width: 40px;
  height: 40px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.icon-button--small button[class*="Polaris-Button"] {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-sm);
}

.icon-button--large button[class*="Polaris-Button"] {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
}

.icon-button:hover button[class*="Polaris-Button"]:not(:disabled) {
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

/* === BUTTON GROUP STYLES === */
.button-group {
  display: flex;
  align-items: center;
}

.button-group--sm {
  gap: var(--space-sm);
}

.button-group--md {
  gap: var(--space-md);
}

.button-group--lg {
  gap: var(--space-lg);
}

.button-group--left {
  justify-content: flex-start;
}

.button-group--center {
  justify-content: center;
}

.button-group--right {
  justify-content: flex-end;
}

.button-group--between {
  justify-content: space-between;
}

/* === FLOATING ACTION BUTTON === */
.fab {
  position: fixed;
  z-index: var(--z-fixed);
}

.fab--bottom-right {
  bottom: var(--space-2xl);
  right: var(--space-2xl);
}

.fab--bottom-left {
  bottom: var(--space-2xl);
  left: var(--space-2xl);
}

.fab--top-right {
  top: var(--space-2xl);
  right: var(--space-2xl);
}

.fab--top-left {
  top: var(--space-2xl);
  left: var(--space-2xl);
}

.fab__button {
  width: 56px;
  height: 56px;
  border-radius: var(--radius-full);
  border: none;
  background: var(--color-primary-600);
  color: white;
  box-shadow: var(--shadow-lg);
  cursor: pointer;
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
}

.fab--large .fab__button {
  width: 64px;
  height: 64px;
  font-size: var(--font-size-2xl);
}

.fab--small .fab__button {
  width: 48px;
  height: 48px;
  font-size: var(--font-size-lg);
}

.fab__button:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-xl);
}

.fab__button:active {
  transform: scale(1.05);
}

/* === SPLIT BUTTON === */
.split-button {
  display: inline-flex;
  position: relative;
}

.split-button__primary {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.split-button__dropdown {
  position: relative;
}

.split-button__dropdown-trigger button[class*="Polaris-Button"] {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;
  padding: 0 var(--space-sm);
  min-width: auto;
}

/* === FOCUS STATES === */
.enhanced-button button[class*="Polaris-Button"]:focus,
.icon-button button[class*="Polaris-Button"]:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* === ACTIVE STATES === */
.enhanced-button button[class*="Polaris-Button"]:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: var(--shadow-xs);
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
  .fab {
    bottom: var(--space-lg);
    right: var(--space-lg);
  }
  
  .button-group {
    flex-wrap: wrap;
  }
  
  .enhanced-button--full-width-mobile {
    width: 100%;
  }
  
  .enhanced-button--full-width-mobile button[class*="Polaris-Button"] {
    width: 100%;
  }
}

/* === ACCESSIBILITY === */
@media (prefers-reduced-motion: reduce) {
  .enhanced-button button[class*="Polaris-Button"],
  .icon-button button[class*="Polaris-Button"],
  .fab__button {
    transition: none;
  }
  
  .enhanced-button:hover button[class*="Polaris-Button"]:not(:disabled),
  .icon-button:hover button[class*="Polaris-Button"]:not(:disabled),
  .fab__button:hover {
    transform: none;
  }
}

/* === HIGH CONTRAST MODE === */
@media (prefers-contrast: high) {
  .enhanced-button button[class*="Polaris-Button"] {
    border-width: 2px;
  }
}
