/* Professional Layout System */

/* === CONTAINER SYSTEM === */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

.container--sm {
  max-width: 640px;
}

.container--md {
  max-width: 768px;
}

.container--lg {
  max-width: 1024px;
}

.container--xl {
  max-width: 1280px;
}

.container--full {
  max-width: none;
}

/* === GRID SYSTEM === */
.grid {
  display: grid;
  gap: var(--space-lg);
}

.grid--cols-1 { grid-template-columns: 1fr; }
.grid--cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid--cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid--cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid--cols-5 { grid-template-columns: repeat(5, 1fr); }
.grid--cols-6 { grid-template-columns: repeat(6, 1fr); }

.grid--gap-xs { gap: var(--space-xs); }
.grid--gap-sm { gap: var(--space-sm); }
.grid--gap-md { gap: var(--space-md); }
.grid--gap-lg { gap: var(--space-lg); }
.grid--gap-xl { gap: var(--space-xl); }
.grid--gap-2xl { gap: var(--space-2xl); }

/* Grid item utilities */
.col-span-1 { grid-column: span 1; }
.col-span-2 { grid-column: span 2; }
.col-span-3 { grid-column: span 3; }
.col-span-4 { grid-column: span 4; }
.col-span-5 { grid-column: span 5; }
.col-span-6 { grid-column: span 6; }
.col-span-full { grid-column: 1 / -1; }

/* === FLEX SYSTEM === */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

/* Justify content */
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

/* Align items */
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-baseline { align-items: baseline; }
.items-stretch { align-items: stretch; }

/* Flex grow/shrink */
.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }

.grow { flex-grow: 1; }
.grow-0 { flex-grow: 0; }
.shrink { flex-shrink: 1; }
.shrink-0 { flex-shrink: 0; }

/* === SPACING SYSTEM === */
/* Padding utilities */
.p-0 { padding: 0; }
.p-xs { padding: var(--space-xs); }
.p-sm { padding: var(--space-sm); }
.p-md { padding: var(--space-md); }
.p-lg { padding: var(--space-lg); }
.p-xl { padding: var(--space-xl); }
.p-2xl { padding: var(--space-2xl); }
.p-3xl { padding: var(--space-3xl); }

/* Padding directional */
.px-xs { padding-left: var(--space-xs); padding-right: var(--space-xs); }
.px-sm { padding-left: var(--space-sm); padding-right: var(--space-sm); }
.px-md { padding-left: var(--space-md); padding-right: var(--space-md); }
.px-lg { padding-left: var(--space-lg); padding-right: var(--space-lg); }
.px-xl { padding-left: var(--space-xl); padding-right: var(--space-xl); }
.px-2xl { padding-left: var(--space-2xl); padding-right: var(--space-2xl); }

.py-xs { padding-top: var(--space-xs); padding-bottom: var(--space-xs); }
.py-sm { padding-top: var(--space-sm); padding-bottom: var(--space-sm); }
.py-md { padding-top: var(--space-md); padding-bottom: var(--space-md); }
.py-lg { padding-top: var(--space-lg); padding-bottom: var(--space-lg); }
.py-xl { padding-top: var(--space-xl); padding-bottom: var(--space-xl); }
.py-2xl { padding-top: var(--space-2xl); padding-bottom: var(--space-2xl); }

.pt-xs { padding-top: var(--space-xs); }
.pt-sm { padding-top: var(--space-sm); }
.pt-md { padding-top: var(--space-md); }
.pt-lg { padding-top: var(--space-lg); }
.pt-xl { padding-top: var(--space-xl); }
.pt-2xl { padding-top: var(--space-2xl); }

.pb-xs { padding-bottom: var(--space-xs); }
.pb-sm { padding-bottom: var(--space-sm); }
.pb-md { padding-bottom: var(--space-md); }
.pb-lg { padding-bottom: var(--space-lg); }
.pb-xl { padding-bottom: var(--space-xl); }
.pb-2xl { padding-bottom: var(--space-2xl); }

.pl-xs { padding-left: var(--space-xs); }
.pl-sm { padding-left: var(--space-sm); }
.pl-md { padding-left: var(--space-md); }
.pl-lg { padding-left: var(--space-lg); }
.pl-xl { padding-left: var(--space-xl); }
.pl-2xl { padding-left: var(--space-2xl); }

.pr-xs { padding-right: var(--space-xs); }
.pr-sm { padding-right: var(--space-sm); }
.pr-md { padding-right: var(--space-md); }
.pr-lg { padding-right: var(--space-lg); }
.pr-xl { padding-right: var(--space-xl); }
.pr-2xl { padding-right: var(--space-2xl); }

/* Margin utilities */
.m-0 { margin: 0; }
.m-xs { margin: var(--space-xs); }
.m-sm { margin: var(--space-sm); }
.m-md { margin: var(--space-md); }
.m-lg { margin: var(--space-lg); }
.m-xl { margin: var(--space-xl); }
.m-2xl { margin: var(--space-2xl); }
.m-auto { margin: auto; }

/* Margin directional */
.mx-xs { margin-left: var(--space-xs); margin-right: var(--space-xs); }
.mx-sm { margin-left: var(--space-sm); margin-right: var(--space-sm); }
.mx-md { margin-left: var(--space-md); margin-right: var(--space-md); }
.mx-lg { margin-left: var(--space-lg); margin-right: var(--space-lg); }
.mx-xl { margin-left: var(--space-xl); margin-right: var(--space-xl); }
.mx-auto { margin-left: auto; margin-right: auto; }

.my-xs { margin-top: var(--space-xs); margin-bottom: var(--space-xs); }
.my-sm { margin-top: var(--space-sm); margin-bottom: var(--space-sm); }
.my-md { margin-top: var(--space-md); margin-bottom: var(--space-md); }
.my-lg { margin-top: var(--space-lg); margin-bottom: var(--space-lg); }
.my-xl { margin-top: var(--space-xl); margin-bottom: var(--space-xl); }
.my-auto { margin-top: auto; margin-bottom: auto; }

.mt-xs { margin-top: var(--space-xs); }
.mt-sm { margin-top: var(--space-sm); }
.mt-md { margin-top: var(--space-md); }
.mt-lg { margin-top: var(--space-lg); }
.mt-xl { margin-top: var(--space-xl); }
.mt-2xl { margin-top: var(--space-2xl); }
.mt-auto { margin-top: auto; }

.mb-xs { margin-bottom: var(--space-xs); }
.mb-sm { margin-bottom: var(--space-sm); }
.mb-md { margin-bottom: var(--space-md); }
.mb-lg { margin-bottom: var(--space-lg); }
.mb-xl { margin-bottom: var(--space-xl); }
.mb-2xl { margin-bottom: var(--space-2xl); }
.mb-auto { margin-bottom: auto; }

.ml-xs { margin-left: var(--space-xs); }
.ml-sm { margin-left: var(--space-sm); }
.ml-md { margin-left: var(--space-md); }
.ml-lg { margin-left: var(--space-lg); }
.ml-xl { margin-left: var(--space-xl); }
.ml-auto { margin-left: auto; }

.mr-xs { margin-right: var(--space-xs); }
.mr-sm { margin-right: var(--space-sm); }
.mr-md { margin-right: var(--space-md); }
.mr-lg { margin-right: var(--space-lg); }
.mr-xl { margin-right: var(--space-xl); }
.mr-auto { margin-right: auto; }

/* === SECTION SPACING === */
.section {
  padding: var(--space-3xl) 0;
}

.section--sm {
  padding: var(--space-2xl) 0;
}

.section--lg {
  padding: var(--space-4xl) 0;
}

.section--xl {
  padding: var(--space-5xl) 0;
}

/* === CONTENT SPACING === */
.content-spacing > * + * {
  margin-top: var(--space-lg);
}

.content-spacing--sm > * + * {
  margin-top: var(--space-md);
}

.content-spacing--lg > * + * {
  margin-top: var(--space-xl);
}

/* === RESPONSIVE GRID === */
@media (max-width: 640px) {
  .grid--cols-2,
  .grid--cols-3,
  .grid--cols-4,
  .grid--cols-5,
  .grid--cols-6 {
    grid-template-columns: 1fr;
  }
  
  .container {
    padding: 0 var(--space-md);
  }
  
  .section {
    padding: var(--space-2xl) 0;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .grid--cols-3,
  .grid--cols-4,
  .grid--cols-5,
  .grid--cols-6 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .grid--cols-4,
  .grid--cols-5,
  .grid--cols-6 {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* === ASPECT RATIOS === */
.aspect-square {
  aspect-ratio: 1 / 1;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

.aspect-photo {
  aspect-ratio: 4 / 3;
}

/* === OVERFLOW === */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }

/* === POSITION === */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* === Z-INDEX === */
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* === WIDTH & HEIGHT === */
.w-full { width: 100%; }
.w-auto { width: auto; }
.h-full { height: 100%; }
.h-auto { height: auto; }
.h-screen { height: 100vh; }

/* === PROFESSIONAL PAGE LAYOUTS === */
.page-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-header {
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  padding: var(--space-lg) 0;
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.page-content {
  flex: 1;
  padding: var(--space-xl) 0;
}

.page-footer {
  background: var(--color-background-secondary);
  border-top: 1px solid var(--color-border);
  padding: var(--space-lg) 0;
  margin-top: auto;
}
