/* === SUPPORT SECTION STYLES === */

/* Main Support Section */
.support-section {
  margin: var(--space-xl) 0;
}

.support-section-content {
  padding: var(--space-lg);
}

.support-info {
  flex: 1;
  margin-bottom: var(--space-md);
}

.support-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-primary-100);
  border-radius: var(--radius-full);
  flex-shrink: 0;
}

.support-text {
  flex: 1;
}

.support-action {
  flex-shrink: 0;
}

.support-features {
  margin-top: var(--space-lg);
  padding-top: var(--space-lg);
  border-top: 1px solid var(--color-border);
}

/* Compact Support Section */
.compact-support-section {
  margin: var(--space-md) 0;
}

.compact-support-info {
  flex: 1;
}

/* Support Banner */
.support-banner {
  margin-bottom: var(--space-lg);
}

.support-banner-content {
  flex: 1;
}

/* Support Contact Options */
.support-contact-options {
  max-width: 300px;
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
  .support-section-content {
    padding: var(--space-md);
  }
  
  .support-section-content .Polaris-InlineStack {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-md);
  }
  
  .support-info {
    margin-bottom: 0;
  }
  
  .support-action {
    align-self: stretch;
  }
  
  .support-action button {
    width: 100%;
  }
  
  .support-banner-content .Polaris-InlineStack {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-sm);
  }
  
  .support-icon {
    width: 40px;
    height: 40px;
  }
}

@media (max-width: 480px) {
  .support-section {
    margin: var(--space-lg) 0;
  }
  
  .support-section-content {
    padding: var(--space-sm);
  }
  
  .support-features {
    margin-top: var(--space-md);
    padding-top: var(--space-md);
  }
  
  .support-icon {
    width: 36px;
    height: 36px;
  }
}

/* === ANIMATION EFFECTS === */
.support-section {
  animation: fadeInUp 0.6s ease-out;
}

.support-action button:hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* === ACCESSIBILITY === */
.support-section button:focus,
.compact-support-section button:focus,
.support-banner button:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* === DARK MODE PREPARATION === */
@media (prefers-color-scheme: dark) {
  .support-icon {
    background: var(--color-primary-200);
  }
  
  .support-features {
    border-top-color: var(--color-border-dark, #333);
  }
}

/* === PRINT STYLES === */
@media print {
  .support-section,
  .compact-support-section,
  .support-banner,
  .support-contact-options {
    display: none;
  }
}
