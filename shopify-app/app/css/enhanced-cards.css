/* Enhanced Card Styles */

/* === BASE CARD STYLES === */
.enhanced-card {
  position: relative;
  transition: var(--transition-normal);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.enhanced-card > div[class*="Polaris-Card"] {
  border-radius: var(--radius-lg);
  transition: var(--transition-normal);
}

/* === CARD VARIANTS === */
.enhanced-card--default > div[class*="Polaris-Card"] {
  border: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
}

.enhanced-card--elevated > div[class*="Polaris-Card"] {
  border: none;
  box-shadow: var(--shadow-md);
  background: var(--color-surface);
}

.enhanced-card--outlined > div[class*="Polaris-Card"] {
  border: 2px solid var(--color-border);
  box-shadow: none;
  background: var(--color-surface);
}

.enhanced-card--subtle > div[class*="Polaris-Card"] {
  border: 1px solid var(--color-border);
  box-shadow: var(--shadow-xs);
  background: var(--color-background-secondary);
}

.enhanced-card--success > div[class*="Polaris-Card"] {
  border: 1px solid var(--color-success-200);
  box-shadow: var(--shadow-sm);
  background: var(--color-success-50);
}

.enhanced-card--warning > div[class*="Polaris-Card"] {
  border: 1px solid var(--color-warning-200);
  box-shadow: var(--shadow-sm);
  background: var(--color-warning-50);
}

.enhanced-card--error > div[class*="Polaris-Card"] {
  border: 1px solid var(--color-error-200);
  box-shadow: var(--shadow-sm);
  background: var(--color-error-50);
}

/* === INTERACTIVE STATES === */
.enhanced-card--interactive {
  cursor: pointer;
}

.enhanced-card--interactive:hover > div[class*="Polaris-Card"] {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-border-hover);
}

.enhanced-card--interactive:active > div[class*="Polaris-Card"] {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.enhanced-card--interactive:focus-within > div[class*="Polaris-Card"] {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* === LOADING STATE === */
.enhanced-card--loading {
  pointer-events: none;
  opacity: 0.7;
}

.enhanced-card__loading {
  padding: var(--space-lg);
}

/* === TEMPLATE CARD STYLES === */
.template-card {
  height: 100%;
  min-width: 300px;
  max-width: 300px;
  cursor: pointer;
}

.template-card__content {
  padding: var(--space-xl);
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.template-card__emoji {
  font-size: var(--font-size-3xl);
  line-height: 1;
  margin-bottom: var(--space-sm);
}

.template-card__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  line-height: var(--line-height-tight);
  margin-bottom: var(--space-xs);
}

.template-card__description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
  flex-grow: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-card__tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
  margin-top: auto;
}

.template-card__tag {
  background: var(--color-primary-100);
  color: var(--color-primary-700);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

/* === STATS CARD STYLES === */
.stats-card__content {
  padding: var(--space-xl);
}

.stats-card__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.stats-card__title {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.stats-card__icon {
  color: var(--color-text-tertiary);
}

.stats-card__value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  line-height: var(--line-height-tight);
  margin-bottom: var(--space-sm);
}

.stats-card__change {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.stats-card__change--positive {
  color: var(--color-success-600);
}

.stats-card__change--negative {
  color: var(--color-error-600);
}

.stats-card__change--neutral {
  color: var(--color-text-tertiary);
}

/* === ACTION CARD STYLES === */
.action-card__content {
  padding: var(--space-xl);
  display: flex;
  align-items: center;
  gap: var(--space-lg);
}

.action-card__icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  background: var(--color-primary-100);
  color: var(--color-primary-600);
}

.action-card--success .action-card__icon {
  background: var(--color-success-100);
  color: var(--color-success-600);
}

.action-card--warning .action-card__icon {
  background: var(--color-warning-100);
  color: var(--color-warning-600);
}

.action-card--error .action-card__icon {
  background: var(--color-error-100);
  color: var(--color-error-600);
}

.action-card__text {
  flex-grow: 1;
}

.action-card__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-xs);
}

.action-card__description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
}

.action-card__button {
  flex-shrink: 0;
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
  .template-card__content {
    padding: var(--space-lg);
  }
  
  .stats-card__content {
    padding: var(--space-lg);
  }
  
  .action-card__content {
    padding: var(--space-lg);
    flex-direction: column;
    text-align: center;
    gap: var(--space-md);
  }
  
  .action-card__icon {
    width: 40px;
    height: 40px;
  }
}

/* === ACCESSIBILITY === */
.enhanced-card--interactive:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
  .enhanced-card,
  .enhanced-card > div[class*="Polaris-Card"] {
    transition: none;
  }
  
  .enhanced-card--interactive:hover > div[class*="Polaris-Card"] {
    transform: none;
  }
}

/* === QUICK ACTION CARD STYLES === */
.quick-action-card {
  height: 100%;
  cursor: pointer;
}

.quick-action-card__content {
  padding: var(--space-lg);
  display: flex;
  align-items: center;
  gap: var(--space-md);
  height: 100%;
}

.quick-action-card__icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  background: var(--color-primary-100);
  color: var(--color-primary-600);
}

.quick-action-card__text {
  flex: 1;
}

.quick-action-card__title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-xs);
}

.quick-action-card__description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
}

.quick-action-card--disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* === METRIC CARD STYLES === */
.metric-card__content {
  padding: var(--space-lg);
}

.metric-card__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.metric-card__title {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.metric-card__icon {
  color: var(--color-text-tertiary);
}

.metric-card__value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  line-height: var(--line-height-tight);
  margin-bottom: var(--space-sm);
}

.metric-card__trend {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.metric-card__trend--up {
  color: var(--color-success-600);
}

.metric-card__trend--down {
  color: var(--color-error-600);
}

.metric-card__trend--neutral {
  color: var(--color-text-tertiary);
}

.metric-card__trend-icon {
  font-size: var(--font-size-sm);
}

/* === DASHBOARD SPECIFIC STYLES === */
.quick-actions-header {
  margin-bottom: var(--space-lg);
}

.quick-actions-recent {
  margin-top: var(--space-lg);
  padding-top: var(--space-lg);
  border-top: 1px solid var(--color-border);
}

.action-category {
  margin-bottom: var(--space-lg);
}

.action-category-grid {
  margin-top: var(--space-md);
}

.realtime-status-header {
  margin-bottom: var(--space-lg);
}

.status-metrics {
  margin-bottom: var(--space-lg);
}

.status-metric {
  min-width: 200px;
}

.job-status-item {
  cursor: pointer;
  transition: var(--transition-fast);
}

.job-status-item:hover {
  transform: translateY(-1px);
}

.job-info {
  flex: 1;
}

.job-progress {
  margin-top: var(--space-sm);
}

.compact-status-item {
  text-align: center;
}

.last-updated {
  text-align: center;
  padding-top: var(--space-md);
  border-top: 1px solid var(--color-border);
}

/* === PROFESSIONAL DASHBOARD === */
.professional-dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.professional-metric-card {
  padding: var(--space-lg);
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.metric-content {
  flex: 1;
}

.metric-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-background-secondary);
  border-radius: var(--radius-md);
}

.metric-trend {
  margin-top: var(--space-sm);
}

.professional-quick-action {
  padding: var(--space-lg);
  cursor: pointer;
  transition: var(--transition-fast);
  border-radius: var(--radius-md);
}

.professional-quick-action:hover {
  background: var(--color-background-secondary);
  transform: translateY(-1px);
}

.action-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-primary-100);
  border-radius: var(--radius-md);
  flex-shrink: 0;
}

.action-content {
  flex: 1;
}

.metrics-section {
  margin-bottom: var(--space-xl);
}

.quick-actions-section {
  margin-bottom: var(--space-xl);
}

.activity-item {
  padding: var(--space-md);
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
}

.activity-item:hover {
  background: var(--color-background-secondary);
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
  .professional-dashboard {
    padding: 0 var(--space-md);
  }

  .professional-metric-card {
    padding: var(--space-md);
    min-height: 100px;
  }

  .professional-quick-action {
    padding: var(--space-md);
  }

  .action-icon,
  .metric-icon {
    width: 32px;
    height: 32px;
  }
}

/* === SIMPLE BILLING COMPONENTS === */
.billing-card-header {
  padding-bottom: var(--space-sm);
}

.usage-overview {
  padding: var(--space-sm) 0;
}

.usage-progress {
  margin-top: var(--space-sm);
}

.usage-warning {
  margin-top: var(--space-sm);
  padding: var(--space-sm);
  background: var(--color-warning-100);
  border-radius: var(--radius-sm);
}

.billing-info {
  padding: var(--space-sm) 0;
}

.billing-actions {
  padding-top: var(--space-sm);
}

.simple-usage-alert {
  margin-bottom: var(--space-md);
}

.billing-metrics-grid {
  margin-top: var(--space-md);
}

.metric-item {
  text-align: center;
  min-width: 80px;
}

/* === DARK MODE PREPARATION === */
@media (prefers-color-scheme: dark) {
  .enhanced-card--default > div[class*="Polaris-Card"],
  .enhanced-card--elevated > div[class*="Polaris-Card"],
  .enhanced-card--outlined > div[class*="Polaris-Card"],
  .enhanced-card--subtle > div[class*="Polaris-Card"] {
    /* Dark mode styles will be added when implementing dark mode */
  }

  .usage-warning {
    background: var(--color-warning-200);
  }
}
