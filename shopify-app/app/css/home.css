.main-content {
    margin-top: 20px;
  }
  
  .submit-button-container {
    display: flex;
    justify-content: flex-end;
  }
  
  .templates-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-top: 8px;
  }
  

  .emoji {
    font-size: 24px;
  }
  
  .badge-container {
    margin-top: 16px;
  }
  
  .task-selection {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    overflow-x: auto;
    white-space: nowrap;
    padding-bottom: 8px;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    align-items: stretch;
    
    &::-webkit-scrollbar {
      height: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
  }
  
.blurred-container {
  position: relative;
  filter: blur(4px);
  opacity: 0.5;
  z-index: 1;
  width: 100%;
}

.full-width-container{
  width: 100%;
}

.task-selection {
  display: flex;
  gap: 16px;
  overflow-x: auto;
  padding: 10px 0;
}


.truncate-2-lines {
  white-space: pre-wrap;
  overflow-wrap: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-height: 1.5em;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 3em;
}

.clickable-box {
  cursor: pointer;
  min-width: 300px;
  max-width: 300px;
}

/* Enhanced Template Card Styling */
.enhanced-template-card {
  transition: all 0.2s ease;
  border-radius: 12px;
  overflow: hidden;
}

.enhanced-template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.enhanced-template-card:active {
  transform: translateY(-1px);
}

.enhanced-template-card .Polaris-Card {
  border-radius: 12px;
  border: 1px solid #e1e3e5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
}

.enhanced-template-card:hover .Polaris-Card {
  border-color: #c9cccf;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

/* Enhanced Main Card Styling */
.enhanced-main-card .Polaris-Card {
  border-radius: 12px;
  border: 1px solid #e1e3e5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
}

.enhanced-main-card {
  border-radius: 12px;
  overflow: hidden;
}
