/* Loading States Styles */

/* === LOADING SPINNER === */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--space-xl);
}

.loading-spinner--overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  z-index: 9998;
}

.loading-spinner__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-md);
}

.loading-overlay {
  position: relative;
}

/* === SKELETON GRIDS === */
.template-skeleton-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-lg);
}

.metric-skeleton-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-md);
}

.quick-actions-skeleton-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--space-md);
}

/* === SKELETON ELEMENTS === */
.skeleton-tags {
  margin-top: var(--space-sm);
}

.skeleton-tag {
  width: 60px;
  height: 20px;
  background: var(--color-background-secondary);
  border-radius: var(--radius-full);
  animation: shimmer 1.5s ease-in-out infinite;
}

.skeleton-badge {
  width: 80px;
  height: 24px;
  background: var(--color-background-secondary);
  border-radius: var(--radius-sm);
  animation: shimmer 1.5s ease-in-out infinite;
}

.skeleton-progress-bar {
  width: 100%;
  height: 8px;
  background: var(--color-background-secondary);
  border-radius: var(--radius-full);
  margin-top: var(--space-sm);
  animation: shimmer 1.5s ease-in-out infinite;
}

/* === JOB STATUS SKELETON === */
.job-skeleton-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.job-skeleton-status {
  flex-shrink: 0;
}

.job-skeleton-progress {
  margin-top: var(--space-sm);
}

/* === CHART SKELETON === */
.chart-skeleton {
  background: var(--color-background-secondary);
  border-radius: var(--radius-md);
  display: flex;
  align-items: end;
  justify-content: center;
  padding: var(--space-lg);
  animation: shimmer 1.5s ease-in-out infinite;
}

.chart-skeleton__content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: end;
  justify-content: center;
}

.chart-skeleton__bars {
  display: flex;
  align-items: end;
  gap: var(--space-sm);
  width: 80%;
  height: 80%;
}

.chart-skeleton__bar {
  flex: 1;
  background: var(--color-surface);
  border-radius: var(--radius-xs);
  min-height: 20%;
  animation: pulse 2s ease-in-out infinite;
}

.chart-skeleton__bar:nth-child(odd) {
  animation-delay: 0.2s;
}

.chart-skeleton__bar:nth-child(even) {
  animation-delay: 0.4s;
}

/* === TABLE SKELETON === */
.table-skeleton {
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.table-skeleton__header {
  display: flex;
  background: var(--color-background-secondary);
  border-bottom: 1px solid var(--color-border);
}

.table-skeleton__header-cell {
  flex: 1;
  padding: var(--space-md);
}

.table-skeleton__row {
  display: flex;
  border-bottom: 1px solid var(--color-border);
}

.table-skeleton__row:last-child {
  border-bottom: none;
}

.table-skeleton__cell {
  flex: 1;
  padding: var(--space-md);
}

/* === QUICK ACTION SKELETON === */
.quick-action-skeleton-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

/* === PROGRESSIVE LOADER === */
.progressive-loader {
  padding: var(--space-xl);
  text-align: center;
}

.progressive-loader__steps {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
  align-items: flex-start;
  max-width: 300px;
  margin: 0 auto;
}

.progressive-loader__step {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  width: 100%;
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
}

.progressive-loader__step--completed {
  background: var(--color-success-100);
  color: var(--color-success-800);
}

.progressive-loader__step--active {
  background: var(--color-primary-100);
  color: var(--color-primary-800);
}

.progressive-loader__step--pending {
  background: var(--color-background-secondary);
  color: var(--color-text-tertiary);
}

.progressive-loader__step-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  flex-shrink: 0;
}

.progressive-loader__step--completed .progressive-loader__step-indicator {
  background: var(--color-success-500);
  color: white;
}

.progressive-loader__step--active .progressive-loader__step-indicator {
  background: var(--color-primary-500);
  color: white;
}

.progressive-loader__step--pending .progressive-loader__step-indicator {
  background: var(--color-background-tertiary);
  color: var(--color-text-tertiary);
}

/* === SHIMMER EFFECT === */
.shimmer-effect {
  background: linear-gradient(
    90deg,
    var(--color-background-secondary) 25%,
    var(--color-background-tertiary) 50%,
    var(--color-background-secondary) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s ease-in-out infinite;
}

.content-placeholder {
  padding: var(--space-md);
}

.content-placeholder__text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.content-placeholder__line {
  margin-bottom: var(--space-xs);
}

.content-placeholder__button {
  margin-top: var(--space-md);
}

/* === ANIMATIONS === */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
  .template-skeleton-grid,
  .quick-actions-skeleton-grid {
    grid-template-columns: 1fr;
  }
  
  .metric-skeleton-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .progressive-loader__steps {
    align-items: center;
  }
  
  .progressive-loader__step {
    flex-direction: column;
    text-align: center;
    gap: var(--space-sm);
  }
}

/* === ACCESSIBILITY === */
@media (prefers-reduced-motion: reduce) {
  .shimmer-effect,
  .skeleton-tag,
  .skeleton-badge,
  .skeleton-progress-bar,
  .chart-skeleton,
  .chart-skeleton__bar {
    animation: none;
  }
  
  .progressive-loader__step {
    transition: none;
  }
}

/* === DARK MODE PREPARATION === */
@media (prefers-color-scheme: dark) {
  .loading-spinner--overlay {
    background: rgba(0, 0, 0, 0.8);
  }
  
  .shimmer-effect {
    background: linear-gradient(
      90deg,
      var(--color-background-secondary-dark, #2a2a2a) 25%,
      var(--color-background-tertiary-dark, #3a3a3a) 50%,
      var(--color-background-secondary-dark, #2a2a2a) 75%
    );
  }
}
