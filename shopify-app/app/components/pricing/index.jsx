import { Layout } from "@shopify/polaris";
import { useLoaderData } from "@remix-run/react";
import { CancelPlanModal } from "./cancelPlanModal";
import { Heading } from "./heading";
import { Plans } from "./plans";
import { PageTitleBar } from "../shared/pageTitleBar";
import PageLayout from "../shared/pageLayout";
import SimpleBillingCard, { SimpleUsageAlert, SimpleBillingMetrics } from "../billing/SimpleBillingCard";
import { ProfessionalNotificationProvider, useProfessionalNotifications } from "../ui/ProfessionalNotifications";
import { useTranslation } from "react-i18next";
import { useState, useCallback } from "react";

// Enhanced Pricing with Professional Billing
const PricingContent = ({ loading, setLoading }) => {
  const { appSubscriptions } = useLoaderData();
  const { t } = useTranslation();
  const notifications = useProfessionalNotifications();
  const [currentPlan = { name: "Free", lineItems: [] }] = appSubscriptions;
  const [showUsageAlert, setShowUsageAlert] = useState(true);

  const handleUpgrade = useCallback(() => {
    notifications.info(t('redirecting_to_upgrade') || 'Redirecting to upgrade...');
    // Upgrade logic would go here
  }, [notifications, t]);

  const handleManageBilling = useCallback(() => {
    notifications.info(t('opening_billing_portal') || 'Opening billing portal...');
    // Billing management logic would go here
  }, [notifications, t]);

  const handleDismissAlert = useCallback(() => {
    setShowUsageAlert(false);
  }, []);

  // Mock usage percentage - replace with real data
  const usagePercentage = 85;

  return (
    <PageLayout
      showBackButton
      title={t("pricing_page_title")}
      subtitle={t("pricing_page_desc")}
    >
      <PageTitleBar title={t("pricing_page")} />
      <Layout>
        {/* Usage Alert */}
        {showUsageAlert && currentPlan.name !== "Free" && (
          <Layout.Section>
            <SimpleUsageAlert
              usagePercentage={usagePercentage}
              onDismiss={handleDismissAlert}
            />
          </Layout.Section>
        )}

        {/* Billing Overview */}
        <Layout.Section>
          <SimpleBillingCard
            currentPlan={currentPlan}
            onUpgrade={handleUpgrade}
            onManageBilling={handleManageBilling}
          />
        </Layout.Section>

        {/* Billing Metrics */}
        <Layout.Section>
          <SimpleBillingMetrics currentPlan={currentPlan} />
        </Layout.Section>

        {/* Current Plan Heading */}
        <Layout.Section>
          <Heading currentPlan={currentPlan} />
        </Layout.Section>

        {/* Available Plans */}
        <Layout.Section>
          <Plans
            currentPlan={currentPlan}
            loading={loading}
            setLoading={setLoading}
          />
        </Layout.Section>
      </Layout>
    </PageLayout>
  );
};

// Main component wrapped with professional notification provider
export default function Pricing({ loading, setLoading }) {
  return (
    <ProfessionalNotificationProvider>
      <PricingContent loading={loading} setLoading={setLoading} />
    </ProfessionalNotificationProvider>
  );
}
