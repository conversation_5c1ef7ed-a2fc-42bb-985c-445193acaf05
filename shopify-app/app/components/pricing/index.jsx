import { Layout } from "@shopify/polaris";
import { useLoaderData } from "@remix-run/react";
import { CancelPlanModal } from "./cancelPlanModal";
import { Heading } from "./heading";
import { Plans } from "./plans";
import { PageTitleBar } from "../shared/pageTitleBar";
import PageLayout from "../shared/pageLayout";
// Temporarily removed to fix UI issues
// import BillingDashboard, { BillingAlerts } from "../billing/BillingDashboard";
// import { SimpleNotificationProvider, useSimpleNotifications } from "../ui/SimpleNotifications";
import { useTranslation } from "react-i18next";

// Reverted to original working version
export default function Pricing({ loading, setLoading }) {
  const { appSubscriptions } = useLoaderData();
  const { t } = useTranslation();
  const [currentPlan = { name: "Free", lineItems: [] }] = appSubscriptions;

  return (
    <PageLayout
      showBackButton
      title={t("pricing_page_title")}
      subtitle={t("pricing_page_desc")}
    >
      <PageTitleBar title={t("pricing_page")} />
      <Layout>
        <Heading currentPlan={currentPlan} />
        <Plans currentPlan={currentPlan} loading={loading} setLoading={setLoading} />
        {/*      <CancelPlanModal currentPlan={currentPlan} /> */}
      </Layout>
    </PageLayout>
  );
}
