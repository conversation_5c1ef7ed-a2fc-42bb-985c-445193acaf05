import React, { memo, useState, useCallback } from "react";

/**
 * Optimized Image Component with lazy loading and error handling
 */
export const OptimizedImage = memo(({ 
  src, 
  alt, 
  className = "", 
  style = {},
  fallbackSrc = "/images/placeholder.png",
  loading = "lazy",
  ...props 
}) => {
  const [imageSrc, setImageSrc] = useState(src);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoad = useCallback(() => {
    setIsLoading(false);
  }, []);

  const handleError = useCallback(() => {
    setHasError(true);
    setIsLoading(false);
    if (imageSrc !== fallbackSrc) {
      setImageSrc(fallbackSrc);
    }
  }, [imageSrc, fallbackSrc]);

  return (
    <div className={`optimized-image-container ${className}`} style={style}>
      {isLoading && !hasError && (
        <div className="image-placeholder" style={{
          backgroundColor: '#f6f6f7',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100px',
          ...style
        }}>
          Loading...
        </div>
      )}
      <img
        src={imageSrc}
        alt={alt}
        loading={loading}
        onLoad={handleLoad}
        onError={handleError}
        style={{
          display: isLoading ? 'none' : 'block',
          ...style
        }}
        {...props}
      />
    </div>
  );
});

OptimizedImage.displayName = 'OptimizedImage';
