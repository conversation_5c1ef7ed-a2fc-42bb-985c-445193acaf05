import React, { memo } from 'react';
import {
  Card,
  BlockStack,
  Text,
  Button,
  InlineStack,
  Icon,
  List
} from '@shopify/polaris';
import {
  QuestionCircleIcon,
  ChatIcon,
  EmailIcon
} from '@shopify/polaris-icons';
import { useTranslation } from 'react-i18next';

// Support Section Component
const SupportSection = memo(({ className = "" }) => {
  const { t } = useTranslation();

  const handleChatWithSupport = () => {
    // Use the existing Intercom integration
    if (window.Intercom) {
      window.Intercom('show');
    } else {
      // Import and use the Intercom utility if window.Intercom is not available
      import('../../utilities/intercom.js').then(({ initializeIntercom }) => {
        initializeIntercom();
        if (window.Intercom) {
          window.Intercom('show');
        } else {
          // Final fallback to email
          window.open('mailto:<EMAIL>?subject=Support Request', '_blank');
        }
      }).catch(() => {
        // Fallback to opening a support email
        window.open('mailto:<EMAIL>?subject=Support Request', '_blank');
      });
    }
  };

  const handleEmailSupport = () => {
    window.open('mailto:<EMAIL>?subject=Support Request', '_blank');
  };

  return (
    <div className={`support-section ${className}`}>
      <Card>
        <div className="support-section-content">
          <InlineStack align="space-between" wrap={false}>
            <div className="support-info">
              <InlineStack gap="300" align="center">
                <div className="support-icon">
                  <Icon source={QuestionCircleIcon} tone="primary" />
                </div>
                <div className="support-text">
                  <Text variant="headingSm" as="h3">
                    {t('need_help_title') || 'Need Help? We\'re Here for You!'}
                  </Text>
                  <Text variant="bodyMd" tone="subdued">
                    {t('support_description') || 'Our support team is ready to assist you at every step. Here\'s what you can expect:'}
                  </Text>
                </div>
              </InlineStack>
            </div>
            <div className="support-action">
              <Button
                variant="primary"
                onClick={handleChatWithSupport}
                icon={ChatIcon}
              >
                {t('chat_with_support') || 'Chat with Support'}
              </Button>
            </div>
          </InlineStack>

          <div className="support-features">
            <List type="bullet">
              <List.Item>
                <Text variant="bodyMd">
                  <strong>{t('fast_responses') || 'Fast responses'}</strong> {t('fast_responses_desc') || 'to your questions and setup needs.'}
                </Text>
              </List.Item>
              <List.Item>
                <Text variant="bodyMd">
                  <strong>{t('expert_advice') || 'Expert advice'}</strong> {t('expert_advice_desc') || 'on optimizing your automation strategy.'}
                </Text>
              </List.Item>
              <List.Item>
                <Text variant="bodyMd">
                  <strong>{t('quick_resolution') || 'Quick resolution'}</strong> {t('quick_resolution_desc') || 'for any technical or account issues.'}
                </Text>
              </List.Item>
            </List>
          </div>
        </div>
      </Card>
    </div>
  );
});

// Compact Support Section (for smaller spaces)
export const CompactSupportSection = memo(({ className = "" }) => {
  const { t } = useTranslation();

  const handleChatWithSupport = () => {
    if (window.Intercom) {
      window.Intercom('show');
    } else {
      window.open('mailto:<EMAIL>?subject=Support Request', '_blank');
    }
  };

  return (
    <div className={`compact-support-section ${className}`}>
      <Card tone="info">
        <InlineStack align="space-between" wrap={false}>
          <div className="compact-support-info">
            <InlineStack gap="200" align="center">
              <Icon source={QuestionCircleIcon} tone="primary" />
              <div>
                <Text variant="bodyMd" fontWeight="semibold">
                  {t('need_help_short') || 'Need help?'}
                </Text>
                <Text variant="bodySm" tone="subdued">
                  {t('support_available') || 'Our team is here to help'}
                </Text>
              </div>
            </InlineStack>
          </div>
          <Button
            variant="primary"
            size="slim"
            onClick={handleChatWithSupport}
          >
            {t('get_help') || 'Get Help'}
          </Button>
        </InlineStack>
      </Card>
    </div>
  );
});

// Support Banner (for top of page)
export const SupportBanner = memo(({ onDismiss, className = "" }) => {
  const { t } = useTranslation();

  const handleChatWithSupport = () => {
    if (window.Intercom) {
      window.Intercom('show');
    } else {
      window.open('mailto:<EMAIL>?subject=Support Request', '_blank');
    }
  };

  return (
    <div className={`support-banner ${className}`}>
      <Card tone="info">
        <InlineStack align="space-between" wrap={false}>
          <div className="support-banner-content">
            <InlineStack gap="300" align="center">
              <Icon source={ChatIcon} tone="primary" />
              <div>
                <Text variant="bodyMd" fontWeight="semibold">
                  {t('welcome_support') || 'Welcome! Need help getting started?'}
                </Text>
                <Text variant="bodySm" tone="subdued">
                  {t('support_team_ready') || 'Our support team is ready to help you set up your first automation.'}
                </Text>
              </div>
            </InlineStack>
          </div>
          <InlineStack gap="200">
            <Button
              variant="primary"
              size="slim"
              onClick={handleChatWithSupport}
            >
              {t('chat_now') || 'Chat Now'}
            </Button>
            {onDismiss && (
              <Button
                variant="plain"
                size="slim"
                onClick={onDismiss}
              >
                {t('dismiss') || 'Dismiss'}
              </Button>
            )}
          </InlineStack>
        </InlineStack>
      </Card>
    </div>
  );
});

// Support Contact Options
export const SupportContactOptions = memo(({ className = "" }) => {
  const { t } = useTranslation();

  const handleChatWithSupport = () => {
    if (window.Intercom) {
      window.Intercom('show');
    } else {
      window.open('mailto:<EMAIL>?subject=Support Request', '_blank');
    }
  };

  const handleEmailSupport = () => {
    window.open('mailto:<EMAIL>?subject=Support Request', '_blank');
  };

  const handleViewDocs = () => {
    window.open('/docs', '_blank');
  };

  return (
    <div className={`support-contact-options ${className}`}>
      <Card>
        <BlockStack gap="400">
          <Text variant="headingSm" as="h3">
            {t('get_support') || 'Get Support'}
          </Text>
          <BlockStack gap="200">
            <Button
              variant="primary"
              fullWidth
              onClick={handleChatWithSupport}
              icon={ChatIcon}
            >
              {t('live_chat') || 'Live Chat'}
            </Button>
            <Button
              variant="secondary"
              fullWidth
              onClick={handleEmailSupport}
              icon={EmailIcon}
            >
              {t('email_support') || 'Email Support'}
            </Button>
            <Button
              variant="plain"
              fullWidth
              onClick={handleViewDocs}
            >
              {t('view_documentation') || 'View Documentation'}
            </Button>
          </BlockStack>
        </BlockStack>
      </Card>
    </div>
  );
});

SupportSection.displayName = 'SupportSection';
CompactSupportSection.displayName = 'CompactSupportSection';
SupportBanner.displayName = 'SupportBanner';
SupportContactOptions.displayName = 'SupportContactOptions';

export default SupportSection;
