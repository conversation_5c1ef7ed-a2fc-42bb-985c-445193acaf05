import React from 'react';
import { 
  Card, 
  BlockStack, 
  Text, 
  Button, 
  InlineStack,
  Icon,
  EmptyState
} from '@shopify/polaris';
import { AlertTriangleIcon, RefreshIcon, BugIcon } from '@shopify/polaris-icons';
import { loggerError } from '../../utilities/helper';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Generate unique error ID for tracking
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Log error details
    loggerError(
      'React Error Boundary caught an error',
      this.props.shopName || 'unknown',
      error.message,
      {
        error: error.toString(),
        errorInfo,
        errorId,
        componentStack: errorInfo.componentStack,
        errorBoundary: this.constructor.name
      }
    );

    this.setState({
      error,
      errorInfo,
      errorId
    });

    // Report to external error tracking service if available
    if (window.Sentry) {
      window.Sentry.captureException(error, {
        tags: {
          errorBoundary: this.constructor.name,
          errorId
        },
        extra: errorInfo
      });
    }

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo, errorId);
    }
  }

  handleRetry = () => {
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null 
    });
    
    // Call custom retry handler if provided
    if (this.props.onRetry) {
      this.props.onRetry();
    }
  };

  handleReportError = () => {
    const { error, errorInfo, errorId } = this.state;
    const errorReport = {
      errorId,
      message: error?.message,
      stack: error?.stack,
      componentStack: errorInfo?.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // Copy error details to clipboard
    navigator.clipboard.writeText(JSON.stringify(errorReport, null, 2))
      .then(() => {
        alert('Error details copied to clipboard. Please share this with support.');
      })
      .catch(() => {
        console.error('Failed to copy error details');
      });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.handleRetry);
      }

      // Default error UI
      return (
        <div className="error-boundary">
          <Card>
            <BlockStack gap="400">
              <div className="error-boundary__header">
                <InlineStack gap="200" align="center">
                  <Icon source={AlertTriangleIcon} tone="critical" />
                  <Text variant="headingMd" as="h2">
                    Something went wrong
                  </Text>
                </InlineStack>
              </div>

              <Text variant="bodyMd">
                We're sorry, but something unexpected happened. Our team has been notified.
              </Text>

              {this.state.errorId && (
                <div className="error-boundary__error-id">
                  <Text variant="bodySm" tone="subdued">
                    Error ID: {this.state.errorId}
                  </Text>
                </div>
              )}

              <InlineStack gap="200">
                <Button
                  variant="primary"
                  icon={RefreshIcon}
                  onClick={this.handleRetry}
                >
                  Try Again
                </Button>
                <Button
                  variant="secondary"
                  icon={BugIcon}
                  onClick={this.handleReportError}
                >
                  Report Error
                </Button>
                {this.props.onContactSupport && (
                  <Button
                    variant="secondary"
                    onClick={this.props.onContactSupport}
                  >
                    Contact Support
                  </Button>
                )}
              </InlineStack>

              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="error-boundary__details">
                  <summary>Error Details (Development Only)</summary>
                  <div className="error-boundary__error-details">
                    <Text variant="bodyMd" fontWeight="semibold">
                      Error: {this.state.error.message}
                    </Text>
                    <pre className="error-boundary__stack">
                      {this.state.error.stack}
                    </pre>
                    {this.state.errorInfo && (
                      <pre className="error-boundary__component-stack">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    )}
                  </div>
                </details>
              )}
            </BlockStack>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// Functional error boundary hook for modern React
export const useErrorHandler = () => {
  return (error, errorInfo) => {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    loggerError(
      'Error caught by error handler',
      'unknown',
      error.message,
      { error: error.toString(), errorInfo, errorId }
    );

    // Report to external service
    if (window.Sentry) {
      window.Sentry.captureException(error, {
        tags: { errorId },
        extra: errorInfo
      });
    }

    return errorId;
  };
};

// Async error boundary for handling promise rejections
export class AsyncErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidMount() {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', this.handlePromiseRejection);
  }

  componentWillUnmount() {
    window.removeEventListener('unhandledrejection', this.handlePromiseRejection);
  }

  handlePromiseRejection = (event) => {
    loggerError(
      'Unhandled promise rejection',
      this.props.shopName || 'unknown',
      event.reason?.message || 'Unknown error',
      { reason: event.reason }
    );

    if (this.props.onAsyncError) {
      this.props.onAsyncError(event.reason);
    }
  };

  componentDidCatch(error, errorInfo) {
    loggerError(
      'Async Error Boundary caught an error',
      this.props.shopName || 'unknown',
      error.message,
      { error: error.toString(), errorInfo }
    );
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <EmptyState
          heading="Something went wrong"
          image=""
          action={{
            content: 'Reload Page',
            onAction: () => window.location.reload()
          }}
        >
          <p>An unexpected error occurred. Please try reloading the page.</p>
        </EmptyState>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for wrapping components with error boundary
export const withErrorBoundary = (Component, errorBoundaryProps = {}) => {
  const WrappedComponent = (props) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

// Error fallback components
export const SimpleErrorFallback = ({ error, retry }) => (
  <Card>
    <BlockStack gap="300">
      <Text variant="headingSm">Oops! Something went wrong</Text>
      <Button onClick={retry}>Try again</Button>
    </BlockStack>
  </Card>
);

export const DetailedErrorFallback = ({ error, retry }) => (
  <Card>
    <BlockStack gap="400">
      <InlineStack gap="200" align="center">
        <Icon source={AlertTriangleIcon} tone="critical" />
        <Text variant="headingMd">Error Occurred</Text>
      </InlineStack>
      <Text variant="bodyMd">{error?.message || 'An unexpected error occurred'}</Text>
      <InlineStack gap="200">
        <Button variant="primary" onClick={retry}>Retry</Button>
        <Button onClick={() => window.location.reload()}>Reload Page</Button>
      </InlineStack>
    </BlockStack>
  </Card>
);

export default ErrorBoundary;
