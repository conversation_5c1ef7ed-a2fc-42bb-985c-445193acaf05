import React, { createContext, useContext, useState, useCallback } from 'react';
import { Banner, BlockStack } from '@shopify/polaris';

// Professional notification context
const ProfessionalNotificationContext = createContext();

// Hook to use professional notifications
export const useProfessionalNotifications = () => {
  const context = useContext(ProfessionalNotificationContext);
  if (!context) {
    // Return a no-op implementation if not wrapped in provider
    return {
      success: () => {},
      error: () => {},
      warning: () => {},
      info: () => {},
      clear: () => {}
    };
  }
  return context;
};

// Professional notification provider
export const ProfessionalNotificationProvider = ({ children }) => {
  const [notification, setNotification] = useState(null);

  const showNotification = useCallback((type, message, options = {}) => {
    const id = Date.now();
    setNotification({
      id,
      type,
      message,
      title: options.title,
      action: options.action,
      ...options
    });

    // Auto-dismiss after duration
    const duration = options.duration || (type === 'error' ? 8000 : 5000);
    if (duration > 0) {
      setTimeout(() => {
        setNotification(prev => prev?.id === id ? null : prev);
      }, duration);
    }
  }, []);

  const clear = useCallback(() => {
    setNotification(null);
  }, []);

  const success = useCallback((message, options = {}) => {
    showNotification('success', message, { title: 'Success', ...options });
  }, [showNotification]);

  const error = useCallback((message, options = {}) => {
    showNotification('critical', message, { title: 'Error', ...options });
  }, [showNotification]);

  const warning = useCallback((message, options = {}) => {
    showNotification('warning', message, { title: 'Warning', ...options });
  }, [showNotification]);

  const info = useCallback((message, options = {}) => {
    showNotification('info', message, { title: 'Info', ...options });
  }, [showNotification]);

  const value = {
    success,
    error,
    warning,
    info,
    clear
  };

  return (
    <ProfessionalNotificationContext.Provider value={value}>
      {children}
      {notification && (
        <div className="professional-notification-overlay">
          <Banner
            title={notification.title}
            tone={notification.type}
            onDismiss={clear}
            action={notification.action}
          >
            {notification.message}
          </Banner>
        </div>
      )}
    </ProfessionalNotificationContext.Provider>
  );
};

// Inline notification component for specific use cases
export const InlineNotification = ({ 
  type = 'info', 
  title, 
  message, 
  onDismiss,
  action,
  className = ""
}) => {
  return (
    <div className={`inline-notification ${className}`}>
      <Banner
        title={title}
        tone={type}
        onDismiss={onDismiss}
        action={action}
      >
        {message}
      </Banner>
    </div>
  );
};

// Status notification for operations
export const StatusNotification = ({ 
  status, 
  title, 
  message, 
  onDismiss,
  action,
  className = ""
}) => {
  const getTone = (status) => {
    switch (status) {
      case 'success': return 'success';
      case 'error': return 'critical';
      case 'warning': return 'warning';
      default: return 'info';
    }
  };

  return (
    <div className={`status-notification ${className}`}>
      <Banner
        title={title}
        tone={getTone(status)}
        onDismiss={onDismiss}
        action={action}
      >
        {message}
      </Banner>
    </div>
  );
};

export default ProfessionalNotificationProvider;
