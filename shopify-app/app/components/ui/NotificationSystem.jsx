import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { Toast, Frame, Banner } from '@shopify/polaris';
import { CheckCircleIcon, AlertTriangleIcon, XCircleIcon, InfoIcon } from '@shopify/polaris-icons';

// Notification types
export const NOTIFICATION_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info'
};

// Notification context
const NotificationContext = createContext();

// Custom hook to use notifications
export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

// Notification provider component
export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);

  const addNotification = useCallback((notification) => {
    const id = Date.now() + Math.random();
    const newNotification = {
      id,
      type: NOTIFICATION_TYPES.INFO,
      duration: 5000,
      title: '', // Add title support
      ...notification,
    };

    setNotifications(prev => [...prev, newNotification]);

    // Auto-remove notification after duration
    if (newNotification.duration > 0) {
      setTimeout(() => {
        removeNotification(id);
      }, newNotification.duration);
    }

    return id;
  }, []);

  const removeNotification = useCallback((id) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  }, []);

  const clearAll = useCallback(() => {
    setNotifications([]);
  }, []);

  // Convenience methods
  const success = useCallback((message, options = {}) => {
    return addNotification({
      type: NOTIFICATION_TYPES.SUCCESS,
      title: options.title || 'Success',
      message,
      ...options
    });
  }, [addNotification]);

  const error = useCallback((message, options = {}) => {
    return addNotification({
      type: NOTIFICATION_TYPES.ERROR,
      title: options.title || 'Error',
      message,
      duration: 8000, // Longer duration for errors
      ...options
    });
  }, [addNotification]);

  const warning = useCallback((message, options = {}) => {
    return addNotification({
      type: NOTIFICATION_TYPES.WARNING,
      title: options.title || 'Warning',
      message,
      duration: 6000,
      ...options
    });
  }, [addNotification]);

  const info = useCallback((message, options = {}) => {
    return addNotification({
      type: NOTIFICATION_TYPES.INFO,
      title: options.title || 'Info',
      message,
      ...options
    });
  }, [addNotification]);

  const value = {
    notifications,
    addNotification,
    removeNotification,
    clearAll,
    success,
    error,
    warning,
    info
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
      <NotificationRenderer notifications={notifications} onDismiss={removeNotification} />
    </NotificationContext.Provider>
  );
};

// Notification renderer component using Banners instead of Toasts
const NotificationRenderer = ({ notifications, onDismiss }) => {
  const getTone = (type) => {
    switch (type) {
      case NOTIFICATION_TYPES.SUCCESS:
        return 'success';
      case NOTIFICATION_TYPES.ERROR:
        return 'critical';
      case NOTIFICATION_TYPES.WARNING:
        return 'warning';
      case NOTIFICATION_TYPES.INFO:
      default:
        return 'info';
    }
  };

  if (notifications.length === 0) return null;

  return (
    <div className="notification-container">
      {notifications.map((notification) => (
        <div key={notification.id} className="notification-item">
          <Banner
            title={notification.title}
            tone={getTone(notification.type)}
            onDismiss={() => onDismiss(notification.id)}
          >
            {notification.message}
          </Banner>
        </div>
      ))}
    </div>
  );
};

// Enhanced Toast component with more features
export const EnhancedToast = ({
  message,
  type = NOTIFICATION_TYPES.INFO,
  action,
  onDismiss,
  persistent = false,
  className = ""
}) => {
  const [visible, setVisible] = useState(true);

  const handleDismiss = () => {
    setVisible(false);
    onDismiss?.();
  };

  if (!visible) return null;

  return (
    <div className={`enhanced-toast enhanced-toast--${type} ${className}`}>
      <div className="enhanced-toast__content">
        <div className="enhanced-toast__message">{message}</div>
        {action && (
          <div className="enhanced-toast__action">
            {action}
          </div>
        )}
      </div>
      {!persistent && (
        <button
          className="enhanced-toast__close"
          onClick={handleDismiss}
          aria-label="Dismiss notification"
        >
          ×
        </button>
      )}
    </div>
  );
};

// Notification banner for important system messages
export const NotificationBanner = ({
  type = NOTIFICATION_TYPES.INFO,
  title,
  message,
  action,
  onDismiss,
  dismissible = true,
  className = ""
}) => {
  const [visible, setVisible] = useState(true);

  const handleDismiss = () => {
    setVisible(false);
    onDismiss?.();
  };

  if (!visible) return null;

  return (
    <div className={`notification-banner notification-banner--${type} ${className}`}>
      <div className="notification-banner__content">
        {title && <div className="notification-banner__title">{title}</div>}
        <div className="notification-banner__message">{message}</div>
        {action && (
          <div className="notification-banner__action">
            {action}
          </div>
        )}
      </div>
      {dismissible && (
        <button
          className="notification-banner__close"
          onClick={handleDismiss}
          aria-label="Dismiss banner"
        >
          ×
        </button>
      )}
    </div>
  );
};

// Progress notification for long-running operations
export const ProgressNotification = ({
  title,
  progress = 0,
  message,
  onCancel,
  className = ""
}) => {
  return (
    <div className={`progress-notification ${className}`}>
      <div className="progress-notification__header">
        <div className="progress-notification__title">{title}</div>
        {onCancel && (
          <button
            className="progress-notification__cancel"
            onClick={onCancel}
            aria-label="Cancel operation"
          >
            Cancel
          </button>
        )}
      </div>
      <div className="progress-notification__progress">
        <div
          className="progress-notification__bar"
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
        />
      </div>
      {message && (
        <div className="progress-notification__message">{message}</div>
      )}
    </div>
  );
};

export default NotificationProvider;
