import React, { memo } from 'react';
import { 
  Card, 
  BlockStack, 
  SkeletonBodyText, 
  SkeletonDisplayText,
  SkeletonThumbnail,
  InlineStack,
  Spinner,
  Text,
  Grid
} from '@shopify/polaris';

// Professional loading spinner
export const ProfessionalSpinner = memo(({ 
  size = "large", 
  message, 
  className = ""
}) => {
  return (
    <div className={`professional-spinner ${className}`}>
      <div className="spinner-content">
        <Spinner accessibilityLabel="Loading" size={size} />
        {message && (
          <Text variant="bodyMd" tone="subdued" alignment="center">
            {message}
          </Text>
        )}
      </div>
    </div>
  );
});

// Professional metric card skeleton
export const ProfessionalMetricSkeleton = memo(({ count = 4 }) => {
  return (
    <Grid columns={{ xs: 1, sm: 2, md: 4, lg: 4, xl: 4 }}>
      {Array.from({ length: count }, (_, index) => (
        <Grid.Cell key={index}>
          <Card>
            <div className="professional-metric-skeleton">
              <InlineStack align="space-between">
                <div className="metric-skeleton-content">
                  <SkeletonBodyText lines={1} />
                  <div className="metric-skeleton-value">
                    <SkeletonDisplayText size="medium" />
                  </div>
                </div>
                <SkeletonThumbnail size="small" />
              </InlineStack>
            </div>
          </Card>
        </Grid.Cell>
      ))}
    </Grid>
  );
});

// Professional quick action skeleton
export const ProfessionalQuickActionSkeleton = memo(({ count = 4 }) => {
  return (
    <Grid columns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2 }}>
      {Array.from({ length: count }, (_, index) => (
        <Grid.Cell key={index}>
          <Card>
            <div className="professional-action-skeleton">
              <InlineStack gap="300" align="center">
                <SkeletonThumbnail size="small" />
                <div className="action-skeleton-content">
                  <SkeletonDisplayText size="small" />
                  <SkeletonBodyText lines={1} />
                </div>
              </InlineStack>
            </div>
          </Card>
        </Grid.Cell>
      ))}
    </Grid>
  );
});

// Professional dashboard skeleton
export const ProfessionalDashboardSkeleton = memo(() => {
  return (
    <div className="professional-dashboard-skeleton">
      <BlockStack gap="600">
        {/* Welcome Section Skeleton */}
        <Card>
          <BlockStack gap="300">
            <SkeletonDisplayText size="medium" />
            <SkeletonBodyText lines={2} />
          </BlockStack>
        </Card>

        {/* Metrics Skeleton */}
        <div className="metrics-skeleton-section">
          <BlockStack gap="400">
            <SkeletonDisplayText size="small" />
            <ProfessionalMetricSkeleton />
          </BlockStack>
        </div>

        {/* Quick Actions Skeleton */}
        <div className="quick-actions-skeleton-section">
          <BlockStack gap="400">
            <SkeletonDisplayText size="small" />
            <ProfessionalQuickActionSkeleton />
          </BlockStack>
        </div>

        {/* Recent Activity Skeleton */}
        <Card>
          <BlockStack gap="400">
            <InlineStack align="space-between">
              <SkeletonDisplayText size="small" />
              <div className="skeleton-button">
                <SkeletonBodyText lines={1} />
              </div>
            </InlineStack>
            <div className="skeleton-divider" />
            <BlockStack gap="200">
              {Array.from({ length: 3 }, (_, index) => (
                <div key={index} className="activity-skeleton-item">
                  <InlineStack align="space-between">
                    <div className="activity-skeleton-content">
                      <SkeletonDisplayText size="small" />
                      <SkeletonBodyText lines={1} />
                    </div>
                    <div className="activity-skeleton-badge">
                      <SkeletonBodyText lines={1} />
                    </div>
                  </InlineStack>
                </div>
              ))}
            </BlockStack>
          </BlockStack>
        </Card>
      </BlockStack>
    </div>
  );
});

// Professional template card skeleton
export const ProfessionalTemplateSkeleton = memo(({ count = 3 }) => {
  return (
    <div className="professional-template-skeleton">
      <Grid columns={{ xs: 1, sm: 2, md: 3, lg: 3, xl: 3 }}>
        {Array.from({ length: count }, (_, index) => (
          <Grid.Cell key={index}>
            <Card>
              <BlockStack gap="300">
                <SkeletonThumbnail size="medium" />
                <SkeletonDisplayText size="small" />
                <SkeletonBodyText lines={2} />
                <div className="template-skeleton-tags">
                  <InlineStack gap="100">
                    <div className="skeleton-tag" />
                    <div className="skeleton-tag" />
                  </InlineStack>
                </div>
                <div className="template-skeleton-button">
                  <SkeletonBodyText lines={1} />
                </div>
              </BlockStack>
            </Card>
          </Grid.Cell>
        ))}
      </Grid>
    </div>
  );
});

// Professional page loading overlay
export const ProfessionalPageLoading = memo(({ message = "Loading..." }) => {
  return (
    <div className="professional-page-loading">
      <div className="page-loading-content">
        <Spinner accessibilityLabel="Loading page" size="large" />
        <Text variant="headingMd" alignment="center">
          {message}
        </Text>
        <Text variant="bodyMd" tone="subdued" alignment="center">
          Please wait while we prepare your dashboard
        </Text>
      </div>
    </div>
  );
});

// Professional content placeholder
export const ProfessionalContentPlaceholder = memo(({ 
  title = "No content available",
  description = "There's nothing to show here yet.",
  action,
  className = ""
}) => {
  return (
    <div className={`professional-content-placeholder ${className}`}>
      <Card>
        <div className="content-placeholder-inner">
          <Text variant="headingMd" alignment="center">
            {title}
          </Text>
          <Text variant="bodyMd" tone="subdued" alignment="center">
            {description}
          </Text>
          {action && (
            <div className="content-placeholder-action">
              {action}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
});

ProfessionalSpinner.displayName = 'ProfessionalSpinner';
ProfessionalMetricSkeleton.displayName = 'ProfessionalMetricSkeleton';
ProfessionalQuickActionSkeleton.displayName = 'ProfessionalQuickActionSkeleton';
ProfessionalDashboardSkeleton.displayName = 'ProfessionalDashboardSkeleton';
ProfessionalTemplateSkeleton.displayName = 'ProfessionalTemplateSkeleton';
ProfessionalPageLoading.displayName = 'ProfessionalPageLoading';
ProfessionalContentPlaceholder.displayName = 'ProfessionalContentPlaceholder';

export default ProfessionalSpinner;
