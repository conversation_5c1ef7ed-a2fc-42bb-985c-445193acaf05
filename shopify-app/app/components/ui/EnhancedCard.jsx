import React, { memo } from "react";
import { Card } from "@shopify/polaris";

/**
 * Enhanced Card Component with professional styling and variants
 */
export const EnhancedCard = memo(({
  children,
  variant = "default",
  interactive = false,
  loading = false,
  className = "",
  onClick,
  ...props
}) => {
  const getCardClasses = () => {
    let classes = "enhanced-card";

    // Add variant classes
    switch (variant) {
      case "elevated":
        classes += " enhanced-card--elevated";
        break;
      case "outlined":
        classes += " enhanced-card--outlined";
        break;
      case "subtle":
        classes += " enhanced-card--subtle";
        break;
      case "success":
        classes += " enhanced-card--success";
        break;
      case "warning":
        classes += " enhanced-card--warning";
        break;
      case "error":
        classes += " enhanced-card--error";
        break;
      default:
        classes += " enhanced-card--default";
    }

    // Add interactive classes
    if (interactive || onClick) {
      classes += " enhanced-card--interactive";
    }

    // Add loading state
    if (loading) {
      classes += " enhanced-card--loading";
    }

    return `${classes} ${className}`;
  };

  const handleClick = (e) => {
    if (onClick && !loading) {
      onClick(e);
    }
  };

  return (
    <div className={getCardClasses()} onClick={handleClick}>
      <Card {...props}>
        {loading ? (
          <div className="enhanced-card__loading">
            <div className="loading-skeleton" style={{ height: '60px', borderRadius: 'var(--radius-md)' }}></div>
          </div>
        ) : (
          children
        )}
      </Card>
    </div>
  );
});

/**
 * Template Card Component - Specialized for template display
 */
export const TemplateCard = memo(({
  template,
  onClick,
  loading = false,
  className = ""
}) => {
  return (
    <EnhancedCard
      variant="elevated"
      interactive
      loading={loading}
      onClick={() => onClick?.(template?.uuid)}
      className={`template-card ${className}`}
    >
      <div className="template-card__content">
        <div className="template-card__emoji">
          {template?.emoji || "🔧"}
        </div>
        <div className="template-card__title">
          {template?.title || "Template"}
        </div>
        <div className="template-card__description">
          {template?.description || "No description available"}
        </div>
        {template?.tags && (
          <div className="template-card__tags">
            {template.tags.slice(0, 3).map((tag, index) => (
              <span key={index} className="template-card__tag">
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>
    </EnhancedCard>
  );
});

/**
 * Stats Card Component - For displaying metrics
 */
export const StatsCard = memo(({
  title,
  value,
  change,
  changeType = "neutral",
  icon,
  loading = false,
  className = ""
}) => {
  return (
    <EnhancedCard
      variant="default"
      loading={loading}
      className={`stats-card ${className}`}
    >
      <div className="stats-card__content">
        <div className="stats-card__header">
          <div className="stats-card__title">{title}</div>
          {icon && <div className="stats-card__icon">{icon}</div>}
        </div>
        <div className="stats-card__value">{value}</div>
        {change && (
          <div className={`stats-card__change stats-card__change--${changeType}`}>
            {change}
          </div>
        )}
      </div>
    </EnhancedCard>
  );
});

/**
 * Action Card Component - For call-to-action cards
 */
export const ActionCard = memo(({
  title,
  description,
  buttonText,
  onAction,
  icon,
  variant = "primary",
  loading = false,
  className = ""
}) => {
  return (
    <EnhancedCard
      variant="outlined"
      className={`action-card action-card--${variant} ${className}`}
      loading={loading}
    >
      <div className="action-card__content">
        {icon && <div className="action-card__icon">{icon}</div>}
        <div className="action-card__text">
          <div className="action-card__title">{title}</div>
          <div className="action-card__description">{description}</div>
        </div>
        {buttonText && (
          <div className="action-card__button">
            <button
              className="btn btn--primary btn--sm"
              onClick={onAction}
              disabled={loading}
            >
              {buttonText}
            </button>
          </div>
        )}
      </div>
    </EnhancedCard>
  );
});

/**
 * Quick Action Card Component - For dashboard quick actions
 */
export const QuickActionCard = memo(({
  title,
  description,
  icon,
  onClick,
  loading = false,
  disabled = false,
  variant = "default",
  className = ""
}) => {
  return (
    <EnhancedCard
      variant={variant}
      interactive
      loading={loading}
      onClick={disabled ? undefined : onClick}
      className={`quick-action-card ${disabled ? 'quick-action-card--disabled' : ''} ${className}`}
    >
      <div className="quick-action-card__content">
        <div className="quick-action-card__icon">
          {icon}
        </div>
        <div className="quick-action-card__text">
          <div className="quick-action-card__title">{title}</div>
          <div className="quick-action-card__description">{description}</div>
        </div>
      </div>
    </EnhancedCard>
  );
});

/**
 * Metric Card Component - For displaying key metrics with trends
 */
export const MetricCard = memo(({
  title,
  value,
  previousValue,
  unit = "",
  trend,
  trendDirection = "neutral",
  icon,
  loading = false,
  className = ""
}) => {
  const calculateTrend = () => {
    if (!previousValue || previousValue === 0) return null;
    const change = ((value - previousValue) / previousValue) * 100;
    return {
      percentage: Math.abs(change).toFixed(1),
      direction: change > 0 ? "up" : change < 0 ? "down" : "neutral"
    };
  };

  const calculatedTrend = trend || calculateTrend();

  return (
    <EnhancedCard
      variant="default"
      loading={loading}
      className={`metric-card ${className}`}
    >
      <div className="metric-card__content">
        <div className="metric-card__header">
          <div className="metric-card__title">{title}</div>
          {icon && <div className="metric-card__icon">{icon}</div>}
        </div>
        <div className="metric-card__value">
          {value}{unit}
        </div>
        {calculatedTrend && (
          <div className={`metric-card__trend metric-card__trend--${calculatedTrend.direction}`}>
            <span className="metric-card__trend-icon">
              {calculatedTrend.direction === "up" ? "↗" : calculatedTrend.direction === "down" ? "↘" : "→"}
            </span>
            {calculatedTrend.percentage}% from last period
          </div>
        )}
      </div>
    </EnhancedCard>
  );
});

EnhancedCard.displayName = 'EnhancedCard';
TemplateCard.displayName = 'TemplateCard';
StatsCard.displayName = 'StatsCard';
ActionCard.displayName = 'ActionCard';
QuickActionCard.displayName = 'QuickActionCard';
MetricCard.displayName = 'MetricCard';
