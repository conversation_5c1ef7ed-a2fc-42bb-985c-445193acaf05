import React, { memo } from 'react';
import { 
  Card, 
  BlockStack, 
  SkeletonBodyText, 
  SkeletonDisplayText,
  SkeletonThumbnail,
  InlineStack,
  Spinner,
  Text
} from '@shopify/polaris';

// Enhanced loading spinner with message
export const LoadingSpinner = memo(({ 
  size = "large", 
  message, 
  overlay = false,
  className = ""
}) => {
  const content = (
    <div className={`loading-spinner ${overlay ? 'loading-spinner--overlay' : ''} ${className}`}>
      <div className="loading-spinner__content">
        <Spinner accessibilityLabel="Loading" size={size} />
        {message && (
          <Text variant="bodyMd" tone="subdued">
            {message}
          </Text>
        )}
      </div>
    </div>
  );

  return overlay ? (
    <div className="loading-overlay">
      {content}
    </div>
  ) : content;
});

// Skeleton card for template loading
export const TemplateCardSkeleton = memo(({ count = 3 }) => {
  return (
    <div className="template-skeleton-grid">
      {Array.from({ length: count }, (_, index) => (
        <Card key={index}>
          <BlockStack gap="300">
            <SkeletonThumbnail size="small" />
            <SkeletonDisplayText size="small" />
            <SkeletonBodyText lines={2} />
            <div className="skeleton-tags">
              <InlineStack gap="100">
                <div className="skeleton-tag" />
                <div className="skeleton-tag" />
              </InlineStack>
            </div>
          </BlockStack>
        </Card>
      ))}
    </div>
  );
});

// Skeleton for metric cards
export const MetricCardSkeleton = memo(({ count = 4 }) => {
  return (
    <div className="metric-skeleton-grid">
      {Array.from({ length: count }, (_, index) => (
        <Card key={index}>
          <BlockStack gap="200">
            <SkeletonBodyText lines={1} />
            <SkeletonDisplayText size="medium" />
            <SkeletonBodyText lines={1} />
          </BlockStack>
        </Card>
      ))}
    </div>
  );
});

// Skeleton for job status items
export const JobStatusSkeleton = memo(({ count = 5 }) => {
  return (
    <BlockStack gap="200">
      {Array.from({ length: count }, (_, index) => (
        <Card key={index}>
          <InlineStack align="space-between">
            <div className="job-skeleton-info">
              <SkeletonDisplayText size="small" />
              <SkeletonBodyText lines={1} />
            </div>
            <div className="job-skeleton-status">
              <div className="skeleton-badge" />
            </div>
          </InlineStack>
          <div className="job-skeleton-progress">
            <div className="skeleton-progress-bar" />
          </div>
        </Card>
      ))}
    </BlockStack>
  );
});

// Skeleton for analytics charts
export const ChartSkeleton = memo(({ height = "300px", title }) => {
  return (
    <Card>
      <BlockStack gap="400">
        {title && <SkeletonDisplayText size="small" />}
        <div className="chart-skeleton" style={{ height }}>
          <div className="chart-skeleton__content">
            <div className="chart-skeleton__bars">
              {Array.from({ length: 6 }, (_, index) => (
                <div 
                  key={index} 
                  className="chart-skeleton__bar"
                  style={{ height: `${Math.random() * 80 + 20}%` }}
                />
              ))}
            </div>
          </div>
        </div>
      </BlockStack>
    </Card>
  );
});

// Skeleton for data table
export const TableSkeleton = memo(({ rows = 5, columns = 3 }) => {
  return (
    <Card>
      <BlockStack gap="300">
        <SkeletonDisplayText size="small" />
        <div className="table-skeleton">
          {/* Header */}
          <div className="table-skeleton__header">
            {Array.from({ length: columns }, (_, index) => (
              <div key={index} className="table-skeleton__header-cell">
                <SkeletonBodyText lines={1} />
              </div>
            ))}
          </div>
          {/* Rows */}
          {Array.from({ length: rows }, (_, rowIndex) => (
            <div key={rowIndex} className="table-skeleton__row">
              {Array.from({ length: columns }, (_, colIndex) => (
                <div key={colIndex} className="table-skeleton__cell">
                  <SkeletonBodyText lines={1} />
                </div>
              ))}
            </div>
          ))}
        </div>
      </BlockStack>
    </Card>
  );
});

// Skeleton for quick actions
export const QuickActionsSkeleton = memo(({ count = 6 }) => {
  return (
    <Card>
      <BlockStack gap="400">
        <SkeletonDisplayText size="medium" />
        <div className="quick-actions-skeleton-grid">
          {Array.from({ length: count }, (_, index) => (
            <Card key={index}>
              <InlineStack gap="300" align="center">
                <SkeletonThumbnail size="small" />
                <div className="quick-action-skeleton-text">
                  <SkeletonDisplayText size="small" />
                  <SkeletonBodyText lines={1} />
                </div>
              </InlineStack>
            </Card>
          ))}
        </div>
      </BlockStack>
    </Card>
  );
});

// Progressive loading component
export const ProgressiveLoader = memo(({ 
  steps = [], 
  currentStep = 0, 
  className = "" 
}) => {
  return (
    <div className={`progressive-loader ${className}`}>
      <BlockStack gap="300">
        <Text variant="headingMd">Loading...</Text>
        <div className="progressive-loader__steps">
          {steps.map((step, index) => (
            <div 
              key={index}
              className={`progressive-loader__step ${
                index < currentStep ? 'progressive-loader__step--completed' :
                index === currentStep ? 'progressive-loader__step--active' :
                'progressive-loader__step--pending'
              }`}
            >
              <div className="progressive-loader__step-indicator">
                {index < currentStep ? '✓' : index === currentStep ? <Spinner size="small" /> : '○'}
              </div>
              <Text variant="bodyMd">{step}</Text>
            </div>
          ))}
        </div>
      </BlockStack>
    </div>
  );
});

// Shimmer effect component
export const ShimmerEffect = memo(({ 
  width = "100%", 
  height = "20px", 
  borderRadius = "4px",
  className = ""
}) => {
  return (
    <div 
      className={`shimmer-effect ${className}`}
      style={{ 
        width, 
        height, 
        borderRadius 
      }}
    />
  );
});

// Content placeholder with shimmer
export const ContentPlaceholder = memo(({ 
  lines = 3, 
  showAvatar = false,
  showButton = false,
  className = ""
}) => {
  return (
    <div className={`content-placeholder ${className}`}>
      <InlineStack gap="300" align="start">
        {showAvatar && (
          <ShimmerEffect width="40px" height="40px" borderRadius="50%" />
        )}
        <div className="content-placeholder__text">
          {Array.from({ length: lines }, (_, index) => (
            <ShimmerEffect 
              key={index}
              width={index === lines - 1 ? "60%" : "100%"}
              height="16px"
              className="content-placeholder__line"
            />
          ))}
          {showButton && (
            <ShimmerEffect 
              width="80px" 
              height="32px" 
              borderRadius="6px"
              className="content-placeholder__button"
            />
          )}
        </div>
      </InlineStack>
    </div>
  );
});

LoadingSpinner.displayName = 'LoadingSpinner';
TemplateCardSkeleton.displayName = 'TemplateCardSkeleton';
MetricCardSkeleton.displayName = 'MetricCardSkeleton';
JobStatusSkeleton.displayName = 'JobStatusSkeleton';
ChartSkeleton.displayName = 'ChartSkeleton';
TableSkeleton.displayName = 'TableSkeleton';
QuickActionsSkeleton.displayName = 'QuickActionsSkeleton';
ProgressiveLoader.displayName = 'ProgressiveLoader';
ShimmerEffect.displayName = 'ShimmerEffect';
ContentPlaceholder.displayName = 'ContentPlaceholder';
