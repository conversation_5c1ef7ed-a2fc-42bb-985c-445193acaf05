import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { Modal, Button, BlockStack, InlineStack, Text, Icon } from '@shopify/polaris';
import { AlertTriangleIcon, InfoIcon, CheckCircleIcon, XCircleIcon } from '@shopify/polaris-icons';

// Modal context
const ModalContext = createContext();

// Custom hook to use modals
export const useModals = () => {
  const context = useContext(ModalContext);
  if (!context) {
    throw new Error('useModals must be used within a ModalProvider');
  }
  return context;
};

// Modal provider component
export const ModalProvider = ({ children }) => {
  const [modals, setModals] = useState([]);

  const openModal = useCallback((modalConfig) => {
    const id = Date.now() + Math.random();
    const modal = {
      id,
      ...modalConfig,
    };
    setModals(prev => [...prev, modal]);
    return id;
  }, []);

  const closeModal = useCallback((id) => {
    setModals(prev => prev.filter(modal => modal.id !== id));
  }, []);

  const closeAllModals = useCallback(() => {
    setModals([]);
  }, []);

  // Convenience methods
  const confirm = useCallback((options) => {
    return new Promise((resolve) => {
      openModal({
        type: 'confirm',
        ...options,
        onConfirm: () => {
          resolve(true);
          closeModal(options.id);
        },
        onCancel: () => {
          resolve(false);
          closeModal(options.id);
        }
      });
    });
  }, [openModal, closeModal]);

  const alert = useCallback((options) => {
    return new Promise((resolve) => {
      openModal({
        type: 'alert',
        ...options,
        onClose: () => {
          resolve();
          closeModal(options.id);
        }
      });
    });
  }, [openModal, closeModal]);

  const prompt = useCallback((options) => {
    return new Promise((resolve) => {
      openModal({
        type: 'prompt',
        ...options,
        onSubmit: (value) => {
          resolve(value);
          closeModal(options.id);
        },
        onCancel: () => {
          resolve(null);
          closeModal(options.id);
        }
      });
    });
  }, [openModal, closeModal]);

  const value = {
    modals,
    openModal,
    closeModal,
    closeAllModals,
    confirm,
    alert,
    prompt
  };

  return (
    <ModalContext.Provider value={value}>
      {children}
      <ModalRenderer modals={modals} onClose={closeModal} />
    </ModalContext.Provider>
  );
};

// Modal renderer component
const ModalRenderer = ({ modals, onClose }) => {
  return (
    <>
      {modals.map((modal) => (
        <ModalComponent
          key={modal.id}
          modal={modal}
          onClose={() => onClose(modal.id)}
        />
      ))}
    </>
  );
};

// Individual modal component
const ModalComponent = ({ modal, onClose }) => {
  const [inputValue, setInputValue] = useState(modal.defaultValue || '');

  const handlePrimaryAction = () => {
    if (modal.type === 'prompt') {
      modal.onSubmit?.(inputValue);
    } else if (modal.type === 'confirm') {
      modal.onConfirm?.();
    } else {
      modal.onClose?.();
    }
  };

  const handleSecondaryAction = () => {
    if (modal.type === 'confirm' || modal.type === 'prompt') {
      modal.onCancel?.();
    } else {
      onClose();
    }
  };

  const getIcon = () => {
    switch (modal.variant) {
      case 'success':
        return <Icon source={CheckCircleIcon} tone="success" />;
      case 'warning':
        return <Icon source={AlertTriangleIcon} tone="warning" />;
      case 'error':
        return <Icon source={XCircleIcon} tone="critical" />;
      case 'info':
      default:
        return <Icon source={InfoIcon} tone="info" />;
    }
  };

  const primaryAction = {
    content: modal.confirmText || 'OK',
    onAction: handlePrimaryAction,
    loading: modal.loading,
    destructive: modal.variant === 'error' || modal.destructive
  };

  const secondaryActions = modal.type === 'confirm' || modal.type === 'prompt' ? [{
    content: modal.cancelText || 'Cancel',
    onAction: handleSecondaryAction
  }] : undefined;

  return (
    <Modal
      open={true}
      onClose={onClose}
      title={modal.title}
      primaryAction={primaryAction}
      secondaryActions={secondaryActions}
      size={modal.size || 'medium'}
      instant={modal.instant}
    >
      <Modal.Section>
        <BlockStack gap="400">
          {modal.variant && (
            <div className="modal-icon">
              {getIcon()}
            </div>
          )}
          
          {modal.message && (
            <Text variant="bodyMd">
              {modal.message}
            </Text>
          )}
          
          {modal.content && modal.content}
          
          {modal.type === 'prompt' && (
            <TextField
              label={modal.inputLabel || 'Enter value'}
              value={inputValue}
              onChange={setInputValue}
              placeholder={modal.placeholder}
              autoComplete="off"
              autoFocus
            />
          )}
        </BlockStack>
      </Modal.Section>
    </Modal>
  );
};

// Enhanced modal components
export const ConfirmationModal = ({ 
  open, 
  onClose, 
  onConfirm, 
  title, 
  message, 
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  destructive = false,
  loading = false 
}) => {
  return (
    <Modal
      open={open}
      onClose={onClose}
      title={title}
      primaryAction={{
        content: confirmText,
        onAction: onConfirm,
        destructive,
        loading
      }}
      secondaryActions={[{
        content: cancelText,
        onAction: onClose
      }]}
    >
      <Modal.Section>
        <Text variant="bodyMd">{message}</Text>
      </Modal.Section>
    </Modal>
  );
};

export const FormModal = ({ 
  open, 
  onClose, 
  onSubmit, 
  title, 
  children,
  submitText = 'Submit',
  cancelText = 'Cancel',
  loading = false,
  size = 'medium'
}) => {
  const handleSubmit = (event) => {
    event.preventDefault();
    const formData = new FormData(event.target);
    onSubmit(Object.fromEntries(formData));
  };

  return (
    <Modal
      open={open}
      onClose={onClose}
      title={title}
      size={size}
      primaryAction={{
        content: submitText,
        onAction: handleSubmit,
        loading
      }}
      secondaryActions={[{
        content: cancelText,
        onAction: onClose
      }]}
    >
      <Modal.Section>
        <form onSubmit={handleSubmit}>
          {children}
        </form>
      </Modal.Section>
    </Modal>
  );
};

export const InfoModal = ({ 
  open, 
  onClose, 
  title, 
  message, 
  variant = 'info',
  actionText = 'OK' 
}) => {
  const getIcon = () => {
    switch (variant) {
      case 'success':
        return <Icon source={CheckCircleIcon} tone="success" />;
      case 'warning':
        return <Icon source={AlertTriangleIcon} tone="warning" />;
      case 'error':
        return <Icon source={XCircleIcon} tone="critical" />;
      case 'info':
      default:
        return <Icon source={InfoIcon} tone="info" />;
    }
  };

  return (
    <Modal
      open={open}
      onClose={onClose}
      title={title}
      primaryAction={{
        content: actionText,
        onAction: onClose
      }}
    >
      <Modal.Section>
        <InlineStack gap="300" align="start">
          <div className="modal-icon">
            {getIcon()}
          </div>
          <Text variant="bodyMd">{message}</Text>
        </InlineStack>
      </Modal.Section>
    </Modal>
  );
};

export const LoadingModal = ({ 
  open, 
  title = 'Loading...', 
  message = 'Please wait while we process your request.',
  progress,
  onCancel 
}) => {
  return (
    <Modal
      open={open}
      title={title}
      secondaryActions={onCancel ? [{
        content: 'Cancel',
        onAction: onCancel
      }] : undefined}
    >
      <Modal.Section>
        <BlockStack gap="400">
          <Text variant="bodyMd">{message}</Text>
          {progress !== undefined && (
            <ProgressBar progress={progress} />
          )}
        </BlockStack>
      </Modal.Section>
    </Modal>
  );
};

export default ModalProvider;
