import React, { createContext, useContext, useState, useCallback } from 'react';
import { Banner, BlockStack } from '@shopify/polaris';

// Simple notification types
export const SIMPLE_NOTIFICATION_TYPES = {
  SUCCESS: 'success',
  ERROR: 'critical',
  WARNING: 'warning',
  INFO: 'info'
};

// Simple notification context
const SimpleNotificationContext = createContext();

// Custom hook to use simple notifications
export const useSimpleNotifications = () => {
  const context = useContext(SimpleNotificationContext);
  if (!context) {
    throw new Error('useSimpleNotifications must be used within a SimpleNotificationProvider');
  }
  return context;
};

// Simple notification provider component
export const SimpleNotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);

  const addNotification = useCallback((notification) => {
    const id = Date.now() + Math.random();
    const newNotification = {
      id,
      tone: SIMPLE_NOTIFICATION_TYPES.INFO,
      duration: 5000,
      dismissible: true,
      ...notification,
    };

    setNotifications(prev => [...prev, newNotification]);

    // Auto-remove notification after duration
    if (newNotification.duration > 0) {
      setTimeout(() => {
        removeNotification(id);
      }, newNotification.duration);
    }

    return id;
  }, []);

  const removeNotification = useCallback((id) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  }, []);

  const clearAll = useCallback(() => {
    setNotifications([]);
  }, []);

  // Convenience methods
  const success = useCallback((message, options = {}) => {
    return addNotification({
      tone: SIMPLE_NOTIFICATION_TYPES.SUCCESS,
      title: options.title || 'Success',
      children: message,
      ...options
    });
  }, [addNotification]);

  const error = useCallback((message, options = {}) => {
    return addNotification({
      tone: SIMPLE_NOTIFICATION_TYPES.ERROR,
      title: options.title || 'Error',
      children: message,
      duration: 8000, // Longer duration for errors
      ...options
    });
  }, [addNotification]);

  const warning = useCallback((message, options = {}) => {
    return addNotification({
      tone: SIMPLE_NOTIFICATION_TYPES.WARNING,
      title: options.title || 'Warning',
      children: message,
      duration: 6000,
      ...options
    });
  }, [addNotification]);

  const info = useCallback((message, options = {}) => {
    return addNotification({
      tone: SIMPLE_NOTIFICATION_TYPES.INFO,
      title: options.title || 'Info',
      children: message,
      ...options
    });
  }, [addNotification]);

  const value = {
    notifications,
    addNotification,
    removeNotification,
    clearAll,
    success,
    error,
    warning,
    info
  };

  return (
    <SimpleNotificationContext.Provider value={value}>
      {children}
      <SimpleNotificationRenderer notifications={notifications} onDismiss={removeNotification} />
    </SimpleNotificationContext.Provider>
  );
};

// Simple notification renderer using Banners
const SimpleNotificationRenderer = ({ notifications, onDismiss }) => {
  if (notifications.length === 0) return null;

  return (
    <div className="simple-notification-container">
      <BlockStack gap="200">
        {notifications.map((notification) => (
          <div key={notification.id} className="simple-notification-item">
            <Banner
              title={notification.title}
              tone={notification.tone}
              onDismiss={notification.dismissible ? () => onDismiss(notification.id) : undefined}
              action={notification.action}
            >
              {notification.children}
            </Banner>
          </div>
        ))}
      </BlockStack>
    </div>
  );
};

// Inline notification component for immediate use
export const InlineNotification = ({ 
  type = 'info', 
  title, 
  message, 
  action,
  onDismiss,
  dismissible = true,
  className = ""
}) => {
  const [visible, setVisible] = useState(true);

  const handleDismiss = () => {
    setVisible(false);
    onDismiss?.();
  };

  if (!visible) return null;

  return (
    <div className={`inline-notification ${className}`}>
      <Banner
        title={title}
        tone={type}
        onDismiss={dismissible ? handleDismiss : undefined}
        action={action}
      >
        {message}
      </Banner>
    </div>
  );
};

// Status notification for operations
export const StatusNotification = ({ 
  status = 'info', 
  title, 
  message, 
  progress,
  onCancel,
  className = ""
}) => {
  const getTone = (status) => {
    switch (status) {
      case 'success': return 'success';
      case 'error': return 'critical';
      case 'warning': return 'warning';
      case 'loading': return 'info';
      default: return 'info';
    }
  };

  const getTitle = (status, title) => {
    if (title) return title;
    switch (status) {
      case 'success': return 'Operation Completed';
      case 'error': return 'Operation Failed';
      case 'warning': return 'Warning';
      case 'loading': return 'Processing...';
      default: return 'Status Update';
    }
  };

  return (
    <div className={`status-notification ${className}`}>
      <Banner
        title={getTitle(status, title)}
        tone={getTone(status)}
        action={onCancel ? {
          content: 'Cancel',
          onAction: onCancel
        } : undefined}
      >
        <div className="status-notification-content">
          {message}
          {progress !== undefined && (
            <div className="status-notification-progress">
              <div className="progress-text">
                {Math.round(progress)}% complete
              </div>
              <div className="progress-bar">
                <div 
                  className="progress-fill"
                  style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
                />
              </div>
            </div>
          )}
        </div>
      </Banner>
    </div>
  );
};

// Global notification manager for use outside React components
class GlobalNotificationManager {
  constructor() {
    this.notifications = [];
    this.listeners = new Set();
  }

  addListener(callback) {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  notify(notification) {
    const id = Date.now() + Math.random();
    const newNotification = { id, ...notification };
    this.notifications.push(newNotification);
    
    this.listeners.forEach(callback => {
      try {
        callback(this.notifications);
      } catch (error) {
        console.error('Notification listener error:', error);
      }
    });

    // Auto-remove after duration
    if (notification.duration > 0) {
      setTimeout(() => {
        this.remove(id);
      }, notification.duration);
    }

    return id;
  }

  remove(id) {
    this.notifications = this.notifications.filter(n => n.id !== id);
    this.listeners.forEach(callback => {
      try {
        callback(this.notifications);
      } catch (error) {
        console.error('Notification listener error:', error);
      }
    });
  }

  clear() {
    this.notifications = [];
    this.listeners.forEach(callback => {
      try {
        callback(this.notifications);
      } catch (error) {
        console.error('Notification listener error:', error);
      }
    });
  }

  success(message, options = {}) {
    return this.notify({
      tone: 'success',
      title: 'Success',
      children: message,
      duration: 5000,
      ...options
    });
  }

  error(message, options = {}) {
    return this.notify({
      tone: 'critical',
      title: 'Error',
      children: message,
      duration: 8000,
      ...options
    });
  }

  warning(message, options = {}) {
    return this.notify({
      tone: 'warning',
      title: 'Warning',
      children: message,
      duration: 6000,
      ...options
    });
  }

  info(message, options = {}) {
    return this.notify({
      tone: 'info',
      title: 'Info',
      children: message,
      duration: 5000,
      ...options
    });
  }
}

// Global instance
export const globalNotifications = new GlobalNotificationManager();

export default SimpleNotificationProvider;
