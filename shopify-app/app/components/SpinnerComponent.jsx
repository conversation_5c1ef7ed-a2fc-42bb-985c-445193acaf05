import React, { memo } from "react";
import "../css/style.css";
import { Spinner } from "@shopify/polaris";
import { useTranslation } from "react-i18next";

export const SpinnerComponent = memo(({ message = "Loading..." }) => {
  const { t } = useTranslation();
  return (
    <div className="spinner-overlay">
      <div className="spinner-content">
        <Spinner />
        <p className="spinner-message">{t(message)}</p>
      </div>
    </div>
  );
});

// Lightweight loading component for quick loads
export const QuickLoader = memo(() => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    padding: '20px'
  }}>
    <Spinner size="small" />
  </div>
));
