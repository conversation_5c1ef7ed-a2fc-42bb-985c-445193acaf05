import React, { memo } from "react";
import appConfig from "../../config/app";
import SideNavigation from "./sideNavigation";
import TabNavigation from "./tabNavigation";

export default memo(function Nav({ isActive }) {
  const { menuData, showSideNavigation, showTabNavigation } = appConfig;

  return (
    <>
      {showSideNavigation && <SideNavigation isActive={isActive} />}
      {showTabNavigation && <TabNavigation menuData={menuData} />}
    </>
  );
});
