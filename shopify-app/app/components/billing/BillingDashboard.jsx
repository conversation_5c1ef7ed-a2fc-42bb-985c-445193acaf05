import React, { useState, useEffect, useCallback, memo } from 'react';
import {
  Card,
  BlockStack,
  Text,
  Badge,
  InlineStack,
  Button,
  ProgressBar,
  Grid,
  Divider,
  Icon,
  Tooltip
} from '@shopify/polaris';
import {
  CreditCardIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  InfoIcon,
  CalendarIcon
} from '@shopify/polaris-icons';
import { MetricCard } from '../ui/EnhancedCard';
import { useTranslation } from 'react-i18next';
import moment from 'moment';

const BillingDashboard = memo(({ 
  subscriptionData, 
  usageData, 
  onUpgrade, 
  onManageBilling,
  loading = false 
}) => {
  const { t } = useTranslation();
  const [billingMetrics, setBillingMetrics] = useState({
    currentUsage: 0,
    usageLimit: 100,
    daysUntilRenewal: 0,
    monthlySpend: 0,
    projectedSpend: 0
  });

  useEffect(() => {
    if (subscriptionData && usageData) {
      const usage = usageData.balanceUsed?.amount || 0;
      const limit = usageData.cappedAmount?.amount || 100;
      const renewalDate = moment(subscriptionData.currentPeriodEnd);
      const daysLeft = renewalDate.diff(moment(), 'days');
      
      setBillingMetrics({
        currentUsage: usage,
        usageLimit: limit,
        daysUntilRenewal: Math.max(0, daysLeft),
        monthlySpend: usage,
        projectedSpend: calculateProjectedSpend(usage, daysLeft)
      });
    }
  }, [subscriptionData, usageData]);

  const calculateProjectedSpend = (currentSpend, daysLeft) => {
    if (daysLeft <= 0) return currentSpend;
    const daysInMonth = 30;
    const dailySpend = currentSpend / (daysInMonth - daysLeft);
    return Math.round(dailySpend * daysInMonth * 100) / 100;
  };

  const getUsageStatus = () => {
    const percentage = (billingMetrics.currentUsage / billingMetrics.usageLimit) * 100;
    if (percentage >= 90) return { tone: 'critical', label: t('usage_critical') };
    if (percentage >= 75) return { tone: 'warning', label: t('usage_high') };
    return { tone: 'success', label: t('usage_normal') };
  };

  const getSubscriptionStatus = () => {
    if (!subscriptionData) return { tone: 'critical', label: t('no_subscription') };
    
    switch (subscriptionData.status) {
      case 'ACTIVE':
        return { tone: 'success', label: t('active') };
      case 'PENDING':
        return { tone: 'warning', label: t('pending') };
      case 'CANCELLED':
        return { tone: 'critical', label: t('cancelled') };
      default:
        return { tone: 'info', label: subscriptionData.status };
    }
  };

  const usageStatus = getUsageStatus();
  const subscriptionStatus = getSubscriptionStatus();
  const usagePercentage = (billingMetrics.currentUsage / billingMetrics.usageLimit) * 100;

  return (
    <div className="billing-dashboard">
      <BlockStack gap="600">
        {/* Billing Overview */}
        <Card>
          <BlockStack gap="400">
            <div className="billing-header">
              <InlineStack align="space-between">
                <div>
                  <Text variant="headingMd" as="h3">
                    {t('billing_overview')}
                  </Text>
                  <Text variant="bodyMd" tone="subdued">
                    {subscriptionData?.name || t('current_plan')}
                  </Text>
                </div>
                <Badge tone={subscriptionStatus.tone}>
                  {subscriptionStatus.label}
                </Badge>
              </InlineStack>
            </div>

            {/* Key Metrics */}
            <Grid columns={{ xs: 1, sm: 2, md: 4, lg: 4, xl: 4 }}>
              <Grid.Cell>
                <MetricCard
                  title={t('current_usage')}
                  value={`$${billingMetrics.currentUsage}`}
                  icon={<Icon source={CreditCardIcon} />}
                  loading={loading}
                />
              </Grid.Cell>
              <Grid.Cell>
                <MetricCard
                  title={t('usage_limit')}
                  value={`$${billingMetrics.usageLimit}`}
                  icon={<Icon source={AlertTriangleIcon} />}
                  loading={loading}
                />
              </Grid.Cell>
              <Grid.Cell>
                <MetricCard
                  title={t('days_until_renewal')}
                  value={billingMetrics.daysUntilRenewal}
                  icon={<Icon source={CalendarIcon} />}
                  loading={loading}
                />
              </Grid.Cell>
              <Grid.Cell>
                <MetricCard
                  title={t('projected_spend')}
                  value={`$${billingMetrics.projectedSpend}`}
                  icon={<Icon source={InfoIcon} />}
                  loading={loading}
                />
              </Grid.Cell>
            </Grid>
          </BlockStack>
        </Card>

        {/* Usage Progress */}
        <Card>
          <BlockStack gap="400">
            <InlineStack align="space-between">
              <Text variant="headingSm" as="h4">
                {t('usage_this_period')}
              </Text>
              <Badge tone={usageStatus.tone}>
                {usageStatus.label}
              </Badge>
            </InlineStack>

            <div className="usage-progress">
              <ProgressBar 
                progress={Math.min(100, usagePercentage)} 
                tone={usagePercentage >= 90 ? 'critical' : usagePercentage >= 75 ? 'warning' : 'primary'}
              />
              <InlineStack align="space-between">
                <Text variant="bodySm" tone="subdued">
                  ${billingMetrics.currentUsage} used
                </Text>
                <Text variant="bodySm" tone="subdued">
                  ${billingMetrics.usageLimit} limit
                </Text>
              </InlineStack>
            </div>

            {usagePercentage >= 75 && (
              <div className="usage-warning">
                <InlineStack gap="200" align="center">
                  <Icon source={AlertTriangleIcon} tone="warning" />
                  <Text variant="bodyMd">
                    {usagePercentage >= 90 
                      ? t('usage_critical_warning')
                      : t('usage_high_warning')
                    }
                  </Text>
                </InlineStack>
              </div>
            )}
          </BlockStack>
        </Card>

        {/* Subscription Details */}
        {subscriptionData && (
          <Card>
            <BlockStack gap="400">
              <Text variant="headingSm" as="h4">
                {t('subscription_details')}
              </Text>

              <div className="subscription-info">
                <InlineStack gap="400" wrap>
                  <div className="subscription-detail">
                    <Text variant="bodySm" tone="subdued">
                      {t('plan_name')}
                    </Text>
                    <Text variant="bodyMd" fontWeight="semibold">
                      {subscriptionData.name}
                    </Text>
                  </div>
                  <div className="subscription-detail">
                    <Text variant="bodySm" tone="subdued">
                      {t('billing_cycle')}
                    </Text>
                    <Text variant="bodyMd" fontWeight="semibold">
                      {t('monthly')}
                    </Text>
                  </div>
                  <div className="subscription-detail">
                    <Text variant="bodySm" tone="subdued">
                      {t('next_billing_date')}
                    </Text>
                    <Text variant="bodyMd" fontWeight="semibold">
                      {moment(subscriptionData.currentPeriodEnd).format('MMM DD, YYYY')}
                    </Text>
                  </div>
                  {subscriptionData.trialDays > 0 && (
                    <div className="subscription-detail">
                      <Text variant="bodySm" tone="subdued">
                        {t('trial_days_remaining')}
                      </Text>
                      <Text variant="bodyMd" fontWeight="semibold">
                        {subscriptionData.trialDays} {t('days')}
                      </Text>
                    </div>
                  )}
                </InlineStack>
              </div>

              <Divider />

              <InlineStack gap="200">
                <Button
                  variant="primary"
                  onClick={onUpgrade}
                  disabled={loading}
                >
                  {t('upgrade_plan')}
                </Button>
                <Button
                  variant="secondary"
                  onClick={onManageBilling}
                  disabled={loading}
                >
                  {t('manage_billing')}
                </Button>
              </InlineStack>
            </BlockStack>
          </Card>
        )}

        {/* Usage History Preview */}
        <Card>
          <BlockStack gap="400">
            <InlineStack align="space-between">
              <Text variant="headingSm" as="h4">
                {t('recent_usage')}
              </Text>
              <Button variant="plain" size="slim">
                {t('view_detailed_usage')}
              </Button>
            </InlineStack>

            <div className="usage-history">
              {/* This would be populated with actual usage data */}
              <Text variant="bodyMd" tone="subdued">
                {t('usage_history_placeholder')}
              </Text>
            </div>
          </BlockStack>
        </Card>
      </BlockStack>
    </div>
  );
});

// Billing alerts component
export const BillingAlerts = memo(({ alerts = [], onDismiss }) => {
  const { t } = useTranslation();

  if (alerts.length === 0) return null;

  return (
    <div className="billing-alerts">
      <BlockStack gap="300">
        {alerts.map((alert, index) => (
          <Card key={index}>
            <InlineStack gap="300" align="space-between">
              <InlineStack gap="200" align="center">
                <Icon 
                  source={alert.type === 'warning' ? AlertTriangleIcon : InfoIcon} 
                  tone={alert.type === 'warning' ? 'warning' : 'info'} 
                />
                <Text variant="bodyMd">{alert.message}</Text>
              </InlineStack>
              {onDismiss && (
                <Button
                  variant="plain"
                  size="slim"
                  onClick={() => onDismiss(index)}
                >
                  {t('dismiss')}
                </Button>
              )}
            </InlineStack>
          </Card>
        ))}
      </BlockStack>
    </div>
  );
});

BillingDashboard.displayName = 'BillingDashboard';
BillingAlerts.displayName = 'BillingAlerts';

export default BillingDashboard;
