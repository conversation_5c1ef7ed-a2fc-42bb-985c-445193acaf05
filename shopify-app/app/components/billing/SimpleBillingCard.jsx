import React, { memo } from 'react';
import {
  Card,
  BlockStack,
  Text,
  Badge,
  InlineStack,
  Button,
  ProgressBar,
  Divider,
  Icon
} from '@shopify/polaris';
import {
  CreditCardIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  CalendarIcon
} from '@shopify/polaris-icons';
import { useTranslation } from 'react-i18next';

// Simple billing overview card
const SimpleBillingCard = memo(({ currentPlan, onUpgrade, onManageBilling }) => {
  const { t } = useTranslation();

  // Mock usage data - replace with real data
  const usageData = {
    currentUsage: 45,
    usageLimit: 100,
    daysUntilRenewal: 12,
    billingCycle: 'monthly'
  };

  const usagePercentage = (usageData.currentUsage / usageData.usageLimit) * 100;

  const getUsageStatus = () => {
    if (usagePercentage >= 90) return { tone: 'critical', label: t('usage_critical') || 'Critical' };
    if (usagePercentage >= 75) return { tone: 'warning', label: t('usage_high') || 'High' };
    return { tone: 'success', label: t('usage_normal') || 'Normal' };
  };

  const getPlanStatus = () => {
    if (currentPlan.name === "Free") return { tone: 'info', label: t('free_plan') || 'Free Plan' };
    return { tone: 'success', label: t('active') || 'Active' };
  };

  const usageStatus = getUsageStatus();
  const planStatus = getPlanStatus();

  return (
    <Card>
      <BlockStack gap="400">
        {/* Plan Header */}
        <div className="billing-card-header">
          <InlineStack align="space-between">
            <div>
              <Text variant="headingSm" as="h3">
                {t('current_plan') || 'Current Plan'}
              </Text>
              <Text variant="bodyMd" fontWeight="semibold">
                {currentPlan.name || 'Free'}
              </Text>
            </div>
            <Badge tone={planStatus.tone}>
              <InlineStack gap="100" align="center">
                <Icon source={CheckCircleIcon} />
                {planStatus.label}
              </InlineStack>
            </Badge>
          </InlineStack>
        </div>

        {/* Usage Overview */}
        {currentPlan.name !== "Free" && (
          <>
            <Divider />
            <div className="usage-overview">
              <BlockStack gap="300">
                <InlineStack align="space-between">
                  <Text variant="bodySm" fontWeight="semibold">
                    {t('usage_this_month') || 'Usage this month'}
                  </Text>
                  <Badge tone={usageStatus.tone}>
                    {usageStatus.label}
                  </Badge>
                </InlineStack>

                <div className="usage-progress">
                  <ProgressBar 
                    progress={Math.min(100, usagePercentage)} 
                    tone={usagePercentage >= 90 ? 'critical' : usagePercentage >= 75 ? 'warning' : 'primary'}
                  />
                  <InlineStack align="space-between">
                    <Text variant="bodySm" tone="subdued">
                      ${usageData.currentUsage} used
                    </Text>
                    <Text variant="bodySm" tone="subdued">
                      ${usageData.usageLimit} limit
                    </Text>
                  </InlineStack>
                </div>

                {usagePercentage >= 75 && (
                  <div className="usage-warning">
                    <InlineStack gap="200" align="center">
                      <Icon source={AlertTriangleIcon} tone="warning" />
                      <Text variant="bodySm">
                        {usagePercentage >= 90 
                          ? t('usage_critical_message') || 'You\'re approaching your usage limit'
                          : t('usage_high_message') || 'Monitor your usage to avoid overages'
                        }
                      </Text>
                    </InlineStack>
                  </div>
                )}
              </BlockStack>
            </div>
          </>
        )}

        {/* Billing Info */}
        <Divider />
        <div className="billing-info">
          <BlockStack gap="200">
            <InlineStack gap="300" align="center">
              <Icon source={CalendarIcon} tone="subdued" />
              <div>
                <Text variant="bodySm" tone="subdued">
                  {t('next_billing_date') || 'Next billing date'}
                </Text>
                <Text variant="bodyMd">
                  {currentPlan.name === "Free" 
                    ? t('no_billing') || 'No billing required'
                    : `${usageData.daysUntilRenewal} days`
                  }
                </Text>
              </div>
            </InlineStack>

            <InlineStack gap="300" align="center">
              <Icon source={CreditCardIcon} tone="subdued" />
              <div>
                <Text variant="bodySm" tone="subdued">
                  {t('billing_cycle') || 'Billing cycle'}
                </Text>
                <Text variant="bodyMd">
                  {currentPlan.name === "Free" 
                    ? t('free') || 'Free'
                    : t('monthly') || 'Monthly'
                  }
                </Text>
              </div>
            </InlineStack>
          </BlockStack>
        </div>

        {/* Actions */}
        <Divider />
        <div className="billing-actions">
          <InlineStack gap="200">
            {currentPlan.name === "Free" ? (
              <Button variant="primary" onClick={onUpgrade}>
                {t('upgrade_plan') || 'Upgrade Plan'}
              </Button>
            ) : (
              <>
                <Button variant="primary" onClick={onUpgrade}>
                  {t('change_plan') || 'Change Plan'}
                </Button>
                <Button variant="secondary" onClick={onManageBilling}>
                  {t('manage_billing') || 'Manage Billing'}
                </Button>
              </>
            )}
          </InlineStack>
        </div>
      </BlockStack>
    </Card>
  );
});

// Simple usage alert component
export const SimpleUsageAlert = memo(({ usagePercentage, onDismiss }) => {
  const { t } = useTranslation();

  if (usagePercentage < 75) return null;

  const isCritical = usagePercentage >= 90;

  return (
    <div className="simple-usage-alert">
      <Card>
        <InlineStack gap="300" align="space-between">
          <InlineStack gap="200" align="center">
            <Icon 
              source={AlertTriangleIcon} 
              tone={isCritical ? 'critical' : 'warning'} 
            />
            <div>
              <Text variant="bodyMd" fontWeight="semibold">
                {isCritical 
                  ? t('usage_critical_title') || 'Usage Critical'
                  : t('usage_high_title') || 'High Usage'
                }
              </Text>
              <Text variant="bodySm" tone="subdued">
                {isCritical 
                  ? t('usage_critical_desc') || 'You\'re close to your limit'
                  : t('usage_high_desc') || 'Monitor your usage'
                }
              </Text>
            </div>
          </InlineStack>
          {onDismiss && (
            <Button variant="plain" size="slim" onClick={onDismiss}>
              {t('dismiss') || 'Dismiss'}
            </Button>
          )}
        </InlineStack>
      </Card>
    </div>
  );
});

// Simple billing metrics component
export const SimpleBillingMetrics = memo(({ currentPlan }) => {
  const { t } = useTranslation();

  // Mock metrics - replace with real data
  const metrics = {
    monthlySpend: 45,
    savedAmount: 120,
    automationsRun: 156
  };

  if (currentPlan.name === "Free") {
    return (
      <Card>
        <BlockStack gap="300">
          <Text variant="headingSm" as="h3">
            {t('upgrade_benefits') || 'Upgrade Benefits'}
          </Text>
          <BlockStack gap="200">
            <Text variant="bodyMd">✓ {t('unlimited_automations') || 'Unlimited automations'}</Text>
            <Text variant="bodyMd">✓ {t('priority_support') || 'Priority support'}</Text>
            <Text variant="bodyMd">✓ {t('advanced_analytics') || 'Advanced analytics'}</Text>
            <Text variant="bodyMd">✓ {t('custom_integrations') || 'Custom integrations'}</Text>
          </BlockStack>
        </BlockStack>
      </Card>
    );
  }

  return (
    <Card>
      <BlockStack gap="400">
        <Text variant="headingSm" as="h3">
          {t('billing_metrics') || 'This Month'}
        </Text>
        <div className="billing-metrics-grid">
          <InlineStack gap="400" wrap>
            <div className="metric-item">
              <Text variant="bodyLg" fontWeight="bold">
                ${metrics.monthlySpend}
              </Text>
              <Text variant="bodySm" tone="subdued">
                {t('spent') || 'Spent'}
              </Text>
            </div>
            <div className="metric-item">
              <Text variant="bodyLg" fontWeight="bold">
                ${metrics.savedAmount}
              </Text>
              <Text variant="bodySm" tone="subdued">
                {t('saved') || 'Saved'}
              </Text>
            </div>
            <div className="metric-item">
              <Text variant="bodyLg" fontWeight="bold">
                {metrics.automationsRun}
              </Text>
              <Text variant="bodySm" tone="subdued">
                {t('automations_run') || 'Automations'}
              </Text>
            </div>
          </InlineStack>
        </div>
      </BlockStack>
    </Card>
  );
});

SimpleBillingCard.displayName = 'SimpleBillingCard';
SimpleUsageAlert.displayName = 'SimpleUsageAlert';
SimpleBillingMetrics.displayName = 'SimpleBillingMetrics';

export default SimpleBillingCard;
