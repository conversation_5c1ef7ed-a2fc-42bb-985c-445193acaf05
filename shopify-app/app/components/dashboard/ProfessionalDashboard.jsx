import React, { memo } from 'react';
import {
  Card,
  BlockStack,
  Text,
  Grid,
  Button,
  InlineStack,
  Icon,
  Badge,
  Divider
} from '@shopify/polaris';
import {
  PlusIcon,
  SettingsIcon,
  ContentIcon,
  ChartVerticalIcon,
  AppsIcon,
  FileIcon
} from '@shopify/polaris-icons';
import { useNavigate } from '@remix-run/react';
import { useTranslation } from 'react-i18next';
import { ProfessionalDashboardSkeleton } from '../ui/ProfessionalLoading';

// Professional metric card component
const ProfessionalMetricCard = memo(({ title, value, subtitle, trend, icon, loading = false }) => {
  return (
    <Card>
      <div className="professional-metric-card">
        <InlineStack align="space-between">
          <div className="metric-content">
            <Text variant="bodySm" tone="subdued">{title}</Text>
            <Text variant="headingLg" as="h3">
              {loading ? '...' : value}
            </Text>
            {subtitle && (
              <Text variant="bodySm" tone="subdued">{subtitle}</Text>
            )}
          </div>
          {icon && (
            <div className="metric-icon">
              <Icon source={icon} tone="subdued" />
            </div>
          )}
        </InlineStack>
        {trend && (
          <div className="metric-trend">
            <Badge tone={trend.direction === 'up' ? 'success' : trend.direction === 'down' ? 'critical' : 'info'}>
              {trend.direction === 'up' ? '↗' : trend.direction === 'down' ? '↘' : '→'} {trend.value}
            </Badge>
          </div>
        )}
      </div>
    </Card>
  );
});

// Professional quick action card
const ProfessionalQuickAction = memo(({ title, description, icon, onClick, variant = "default" }) => {
  return (
    <Card>
      <div className="professional-quick-action" onClick={onClick}>
        <InlineStack gap="300" align="center">
          <div className="action-icon">
            <Icon source={icon} tone="primary" />
          </div>
          <div className="action-content">
            <Text variant="bodyMd" fontWeight="semibold">{title}</Text>
            <Text variant="bodySm" tone="subdued">{description}</Text>
          </div>
        </InlineStack>
      </div>
    </Card>
  );
});

// Main professional dashboard component
const ProfessionalDashboard = memo(({ shopName, templates = [], workers = [], loading = false }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // Show loading skeleton while data is being fetched
  if (loading) {
    return <ProfessionalDashboardSkeleton />;
  }

  // Calculate metrics from existing data
  const metrics = {
    totalTemplates: templates.length || 0,
    activeWorkers: workers.filter(w => w.status === 'active').length || 0,
    completedJobs: workers.filter(w => w.status === 'completed').length || 0,
    successRate: workers.length > 0 ? Math.round((workers.filter(w => w.status === 'completed').length / workers.length) * 100) : 0
  };

  const quickActions = [
    {
      id: 'create-workflow',
      title: t('create_workflow') || 'Create Workflow',
      description: t('start_new_automation') || 'Start building automation',
      icon: PlusIcon,
      onClick: () => navigate('/app/create/workers')
    },
    {
      id: 'browse-templates',
      title: t('browse_templates') || 'Browse Templates',
      description: t('explore_templates') || 'Explore ready-made solutions',
      icon: ContentIcon,
      onClick: () => navigate('/app/templates')
    },
    {
      id: 'view-analytics',
      title: t('view_analytics') || 'View Analytics',
      description: t('track_performance') || 'Track performance metrics',
      icon: ChartVerticalIcon,
      onClick: () => navigate('/app/analytics')
    },
    {
      id: 'manage-workers',
      title: t('manage_workers') || 'Manage Workers',
      description: t('view_all_workers') || 'View and manage all workers',
      icon: AppsIcon,
      onClick: () => navigate('/app/workers')
    }
  ];

  return (
    <div className="professional-dashboard">
      <BlockStack gap="600">
        {/* Welcome Section */}
        <Card>
          <BlockStack gap="300">
            <Text variant="headingMd" as="h2">
              {t('welcome_back') || 'Welcome back'}
            </Text>
            <Text variant="bodyMd" tone="subdued">
              {t('dashboard_subtitle') || 'Here\'s what\'s happening with your store automation'}
            </Text>
          </BlockStack>
        </Card>

        {/* Metrics Overview */}
        <div className="metrics-section">
          <BlockStack gap="400">
            <Text variant="headingSm" as="h3">
              {t('overview') || 'Overview'}
            </Text>
            <Grid columns={{ xs: 1, sm: 2, md: 4, lg: 4, xl: 4 }}>
              <Grid.Cell>
                <ProfessionalMetricCard
                  title={t('total_templates') || 'Total Templates'}
                  value={metrics.totalTemplates}
                  icon={ContentIcon}
                />
              </Grid.Cell>
              <Grid.Cell>
                <ProfessionalMetricCard
                  title={t('active_workers') || 'Active Workers'}
                  value={metrics.activeWorkers}
                  icon={AppsIcon}
                />
              </Grid.Cell>
              <Grid.Cell>
                <ProfessionalMetricCard
                  title={t('completed_jobs') || 'Completed Jobs'}
                  value={metrics.completedJobs}
                  icon={FileIcon}
                />
              </Grid.Cell>
              <Grid.Cell>
                <ProfessionalMetricCard
                  title={t('success_rate') || 'Success Rate'}
                  value={`${metrics.successRate}%`}
                  icon={ChartVerticalIcon}
                  trend={metrics.successRate >= 80 ? { direction: 'up', value: 'Good' } : { direction: 'neutral', value: 'OK' }}
                />
              </Grid.Cell>
            </Grid>
          </BlockStack>
        </div>

        {/* Quick Actions */}
        <div className="quick-actions-section">
          <BlockStack gap="400">
            <Text variant="headingSm" as="h3">
              {t('quick_actions') || 'Quick Actions'}
            </Text>
            <Grid columns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2 }}>
              {quickActions.map((action) => (
                <Grid.Cell key={action.id}>
                  <ProfessionalQuickAction
                    title={action.title}
                    description={action.description}
                    icon={action.icon}
                    onClick={action.onClick}
                  />
                </Grid.Cell>
              ))}
            </Grid>
          </BlockStack>
        </div>

        {/* Recent Activity */}
        {workers.length > 0 && (
          <Card>
            <BlockStack gap="400">
              <InlineStack align="space-between">
                <Text variant="headingSm" as="h3">
                  {t('recent_activity') || 'Recent Activity'}
                </Text>
                <Button variant="plain" onClick={() => navigate('/app/workers')}>
                  {t('view_all') || 'View all'}
                </Button>
              </InlineStack>
              <Divider />
              <BlockStack gap="200">
                {workers.slice(0, 3).map((worker, index) => (
                  <div key={index} className="activity-item">
                    <InlineStack align="space-between">
                      <div>
                        <Text variant="bodyMd">{worker.title || `Worker ${index + 1}`}</Text>
                        <Text variant="bodySm" tone="subdued">
                          {worker.updatedAt ? new Date(worker.updatedAt).toLocaleDateString() : 'Recently'}
                        </Text>
                      </div>
                      <Badge tone={worker.status === 'completed' ? 'success' : worker.status === 'failed' ? 'critical' : 'info'}>
                        {worker.status || 'Active'}
                      </Badge>
                    </InlineStack>
                  </div>
                ))}
              </BlockStack>
            </BlockStack>
          </Card>
        )}
      </BlockStack>
    </div>
  );
});

ProfessionalMetricCard.displayName = 'ProfessionalMetricCard';
ProfessionalQuickAction.displayName = 'ProfessionalQuickAction';
ProfessionalDashboard.displayName = 'ProfessionalDashboard';

export default ProfessionalDashboard;
