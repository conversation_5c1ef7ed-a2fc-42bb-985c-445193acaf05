import React, { memo } from 'react';
import {
  Card,
  BlockStack,
  Text,
  Grid,
  Button,
  InlineStack,
  Icon
} from '@shopify/polaris';
import {
  PlusIcon,
  SettingsIcon,
  ContentIcon,
  ChartVerticalIcon,
  AppsIcon,
  FileIcon,
  ConnectIcon,
  CodeIcon
} from '@shopify/polaris-icons';
import { QuickActionCard } from '../ui/EnhancedCard';
import { useNavigate } from '@remix-run/react';
import { useTranslation } from 'react-i18next';
import { trackButtonClick } from '../../utilities/mixpanel';

const QuickActions = memo(({ shopName, onActionClick }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleActionClick = (action, path) => {
    trackButtonClick(`Quick Action: ${action}`, 'Dashboard', { shop_name: shopName });
    onActionClick?.(action);
    if (path) {
      navigate(path);
    }
  };

  const quickActions = [
    {
      id: 'create-workflow',
      title: t('create_new_workflow'),
      description: t('start_building_automation'),
      icon: <Icon source={PlusIcon} tone="primary" />,
      path: '/app/create/workers',
      variant: 'elevated'
    },
    {
      id: 'browse-templates',
      title: t('browse_templates'),
      description: t('explore_ready_made_solutions'),
      icon: <Icon source={ContentIcon} tone="primary" />,
      path: '/app/templates',
      variant: 'default'
    },
    {
      id: 'view-analytics',
      title: t('view_analytics'),
      description: t('track_performance_metrics'),
      icon: <Icon source={ChartVerticalIcon} tone="primary" />,
      path: '/app/analytics',
      variant: 'default'
    },
    {
      id: 'manage-integrations',
      title: t('manage_integrations'),
      description: t('connect_your_tools'),
      icon: <Icon source={ConnectIcon} tone="primary" />,
      path: '/app/integrations',
      variant: 'default'
    },
    {
      id: 'workflow-settings',
      title: t('workflow_settings'),
      description: t('configure_automation_rules'),
      icon: <Icon source={SettingsIcon} tone="primary" />,
      path: '/app/settings',
      variant: 'default'
    },
    {
      id: 'generate-report',
      title: t('generate_report'),
      description: t('export_performance_data'),
      icon: <Icon source={FileIcon} tone="primary" />,
      onClick: () => handleActionClick('generate-report'),
      variant: 'default'
    }
  ];

  return (
    <Card>
      <BlockStack gap="400">
        <div className="quick-actions-header">
          <Text variant="headingMd" as="h3">
            {t('quick_actions')}
          </Text>
          <Text variant="bodyMd" as="p" tone="subdued">
            {t('quick_actions_description')}
          </Text>
        </div>

        <Grid columns={{ xs: 1, sm: 2, md: 3, lg: 3, xl: 3 }}>
          {quickActions.map((action) => (
            <Grid.Cell key={action.id}>
              <QuickActionCard
                title={action.title}
                description={action.description}
                icon={action.icon}
                variant={action.variant}
                onClick={() => handleActionClick(action.id, action.path)}
                className="quick-action-item"
              />
            </Grid.Cell>
          ))}
        </Grid>

        {/* Recent Activity Quick Access */}
        <div className="quick-actions-recent">
          <Text variant="headingSm" as="h4">
            {t('recent_activity')}
          </Text>
          <InlineStack gap="200" wrap>
            <Button
              size="slim"
              onClick={() => handleActionClick('view-recent-jobs', '/app/workers')}
            >
              {t('view_recent_jobs')}
            </Button>
            <Button
              size="slim"
              onClick={() => handleActionClick('check-job-status')}
            >
              {t('check_job_status')}
            </Button>
            <Button
              size="slim"
              onClick={() => handleActionClick('view-logs')}
            >
              {t('view_logs')}
            </Button>
          </InlineStack>
        </div>
      </BlockStack>
    </Card>
  );
});

// Enhanced Quick Action Grid with categories
export const CategorizedQuickActions = memo(({ shopName, onActionClick }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleActionClick = (action, path) => {
    trackButtonClick(`Quick Action: ${action}`, 'Dashboard', { shop_name: shopName });
    onActionClick?.(action);
    if (path) {
      navigate(path);
    }
  };

  const actionCategories = [
    {
      title: t('automation'),
      actions: [
        {
          id: 'create-workflow',
          title: t('create_workflow'),
          description: t('build_new_automation'),
          icon: <Icon source={PlusIcon} tone="primary" />,
          path: '/app/create/workers'
        },
        {
          id: 'manage-workflows',
          title: t('manage_workflows'),
          description: t('edit_existing_automations'),
          icon: <Icon source={AppsIcon} tone="primary" />,
          path: '/app/workers'
        }
      ]
    },
    {
      title: t('templates'),
      actions: [
        {
          id: 'browse-templates',
          title: t('browse_templates'),
          description: t('explore_templates'),
          icon: <Icon source={ContentIcon} tone="primary" />,
          path: '/app/templates'
        },
        {
          id: 'create-template',
          title: t('create_template'),
          description: t('save_as_template'),
          icon: <Icon source={CodeIcon} tone="primary" />,
          onClick: () => handleActionClick('create-template')
        }
      ]
    },
    {
      title: t('insights'),
      actions: [
        {
          id: 'view-analytics',
          title: t('analytics'),
          description: t('performance_insights'),
          icon: <Icon source={ChartVerticalIcon} tone="primary" />,
          path: '/app/analytics'
        },
        {
          id: 'export-data',
          title: t('export_data'),
          description: t('download_reports'),
          icon: <Icon source={FileIcon} tone="primary" />,
          onClick: () => handleActionClick('export-data')
        }
      ]
    }
  ];

  return (
    <Card>
      <BlockStack gap="600">
        <div className="categorized-actions-header">
          <Text variant="headingMd" as="h3">
            {t('quick_actions')}
          </Text>
        </div>

        {actionCategories.map((category, categoryIndex) => (
          <div key={categoryIndex} className="action-category">
            <Text variant="headingSm" as="h4" tone="subdued">
              {category.title}
            </Text>
            <div className="action-category-grid">
              <Grid columns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2 }}>
                {category.actions.map((action) => (
                  <Grid.Cell key={action.id}>
                    <QuickActionCard
                      title={action.title}
                      description={action.description}
                      icon={action.icon}
                      onClick={() => handleActionClick(action.id, action.path)}
                      className="categorized-action-item"
                    />
                  </Grid.Cell>
                ))}
              </Grid>
            </div>
          </div>
        ))}
      </BlockStack>
    </Card>
  );
});

// Compact Quick Actions for smaller spaces
export const CompactQuickActions = memo(({ shopName, onActionClick }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleActionClick = (action, path) => {
    trackButtonClick(`Compact Action: ${action}`, 'Dashboard', { shop_name: shopName });
    onActionClick?.(action);
    if (path) {
      navigate(path);
    }
  };

  const compactActions = [
    {
      id: 'create',
      label: t('create'),
      icon: PlusIcon,
      path: '/app/create/workers',
      variant: 'primary'
    },
    {
      id: 'templates',
      label: t('templates'),
      icon: ContentIcon,
      path: '/app/templates',
      variant: 'secondary'
    },
    {
      id: 'analytics',
      label: t('analytics'),
      icon: ChartVerticalIcon,
      path: '/app/analytics',
      variant: 'secondary'
    },
    {
      id: 'settings',
      label: t('settings'),
      icon: SettingsIcon,
      path: '/app/settings',
      variant: 'secondary'
    }
  ];

  return (
    <Card>
      <BlockStack gap="300">
        <Text variant="headingSm" as="h4">
          {t('quick_actions')}
        </Text>
        <InlineStack gap="200" wrap>
          {compactActions.map((action) => (
            <Button
              key={action.id}
              variant={action.variant}
              size="medium"
              icon={action.icon}
              onClick={() => handleActionClick(action.id, action.path)}
            >
              {action.label}
            </Button>
          ))}
        </InlineStack>
      </BlockStack>
    </Card>
  );
});

QuickActions.displayName = 'QuickActions';
CategorizedQuickActions.displayName = 'CategorizedQuickActions';
CompactQuickActions.displayName = 'CompactQuickActions';

export default QuickActions;
