import React, { useState, useEffect, useCallback, memo } from 'react';
import {
  Card,
  BlockStack,
  Text,
  Badge,
  InlineStack,
  ProgressBar,
  Button,
  Icon,
  Spinner,
  EmptyState
} from '@shopify/polaris';
import {
  CheckCircleIcon,
  AlertCircleIcon,
  ClockIcon,
  RefreshIcon,
  PlayIcon,
  PauseCircleIcon
} from '@shopify/polaris-icons';
import { MetricCard } from '../ui/EnhancedCard';
import { useTranslation } from 'react-i18next';
import moment from 'moment';

// Real-time job status component
const RealTimeStatus = memo(({
  shopName,
  refreshInterval = 30000, // 30 seconds
  onJobClick,
  onRefresh
}) => {
  const { t } = useTranslation();
  const [jobs, setJobs] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [systemStatus, setSystemStatus] = useState('operational');

  // Fetch job status
  const fetchJobStatus = useCallback(async () => {
    setIsLoading(true);
    try {
      // Simulate API call - replace with actual API
      const response = await fetch('/api/jobs/status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ functionType: 'getJobStatus' })
      });

      if (response.ok) {
        const data = await response.json();
        setJobs(data.jobs || []);
        setSystemStatus(data.systemStatus || 'operational');
        setLastUpdated(new Date());
      }
    } catch (error) {
      console.error('Failed to fetch job status:', error);
      setSystemStatus('degraded');
    } finally {
      setIsLoading(false);
      onRefresh?.();
    }
  }, [onRefresh]);

  // Auto-refresh effect
  useEffect(() => {
    fetchJobStatus();

    if (autoRefresh) {
      const interval = setInterval(fetchJobStatus, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [fetchJobStatus, autoRefresh, refreshInterval]);

  const getStatusBadge = (status) => {
    const statusConfig = {
      running: { tone: 'info', icon: ClockIcon, label: t('running') },
      completed: { tone: 'success', icon: CheckCircleIcon, label: t('completed') },
      failed: { tone: 'critical', icon: AlertCircleIcon, label: t('failed') },
      pending: { tone: 'warning', icon: ClockIcon, label: t('pending') }
    };

    const config = statusConfig[status] || statusConfig.pending;

    return (
      <Badge tone={config.tone}>
        <InlineStack gap="100" align="center">
          <Icon source={config.icon} />
          {config.label}
        </InlineStack>
      </Badge>
    );
  };

  const getSystemStatusColor = () => {
    switch (systemStatus) {
      case 'operational': return 'success';
      case 'degraded': return 'warning';
      case 'outage': return 'critical';
      default: return 'info';
    }
  };

  const runningJobs = jobs.filter(job => job.status === 'running');
  const completedJobs = jobs.filter(job => job.status === 'completed');
  const failedJobs = jobs.filter(job => job.status === 'failed');

  return (
    <Card>
      <BlockStack gap="400">
        {/* Header with system status */}
        <div className="realtime-status-header">
          <InlineStack align="space-between">
            <div>
              <Text variant="headingMd" as="h3">
                {t('real_time_status')}
              </Text>
              <InlineStack gap="200" align="center">
                <Text variant="bodyMd" tone="subdued">
                  {t('system_status')}:
                </Text>
                <Badge tone={getSystemStatusColor()}>
                  {t(systemStatus)}
                </Badge>
              </InlineStack>
            </div>
            <InlineStack gap="200">
              <Button
                size="slim"
                icon={autoRefresh ? PauseCircleIcon : PlayIcon}
                onClick={() => setAutoRefresh(!autoRefresh)}
              >
                {autoRefresh ? t('pause') : t('resume')}
              </Button>
              <Button
                size="slim"
                icon={RefreshIcon}
                loading={isLoading}
                onClick={fetchJobStatus}
              >
                {t('refresh')}
              </Button>
            </InlineStack>
          </InlineStack>
        </div>

        {/* Status metrics */}
        <div className="status-metrics">
          <InlineStack gap="400" wrap>
            <MetricCard
              title={t('running_jobs')}
              value={runningJobs.length}
              icon={<Icon source={ClockIcon} tone="info" />}
              className="status-metric"
            />
            <MetricCard
              title={t('completed_today')}
              value={completedJobs.length}
              icon={<Icon source={CheckCircleIcon} tone="success" />}
              className="status-metric"
            />
            <MetricCard
              title={t('failed_jobs')}
              value={failedJobs.length}
              icon={<Icon source={AlertCircleIcon} tone="critical" />}
              className="status-metric"
            />
          </InlineStack>
        </div>

        {/* Active jobs list */}
        <div className="active-jobs">
          <Text variant="headingSm" as="h4">
            {t('active_jobs')}
          </Text>

          {jobs.length === 0 ? (
            <EmptyState
              heading={t('no_active_jobs')}
              image=""
            >
              <Text>{t('no_jobs_running_currently')}</Text>
            </EmptyState>
          ) : (
            <BlockStack gap="200">
              {jobs.slice(0, 5).map((job) => (
                <div
                  key={job.id}
                  className="job-status-item"
                  onClick={() => onJobClick?.(job)}
                >
                  <Card>
                    <InlineStack align="space-between">
                      <div className="job-info">
                        <Text variant="bodyMd" fontWeight="semibold">
                          {job.title || `Job ${job.id}`}
                        </Text>
                        <Text variant="bodySm" tone="subdued">
                          {t('started')} {moment(job.startedAt).fromNow()}
                        </Text>
                      </div>
                      <div className="job-status">
                        {getStatusBadge(job.status)}
                      </div>
                    </InlineStack>

                    {job.progress !== undefined && (
                      <div className="job-progress">
                        <ProgressBar
                          progress={job.progress}
                          tone={job.status === 'failed' ? 'critical' : 'primary'}
                        />
                        <Text variant="bodySm" tone="subdued">
                          {job.progress}% complete
                        </Text>
                      </div>
                    )}
                  </Card>
                </div>
              ))}
            </BlockStack>
          )}
        </div>

        {/* Last updated info */}
        {lastUpdated && (
          <div className="last-updated">
            <Text variant="bodySm" tone="subdued">
              {t('last_updated')}: {moment(lastUpdated).format('HH:mm:ss')}
            </Text>
          </div>
        )}
      </BlockStack>
    </Card>
  );
});

// Compact status widget for smaller spaces
export const CompactStatusWidget = memo(({
  shopName,
  refreshInterval = 60000
}) => {
  const { t } = useTranslation();
  const [status, setStatus] = useState({
    running: 0,
    completed: 0,
    failed: 0,
    systemStatus: 'operational'
  });
  const [isLoading, setIsLoading] = useState(false);

  const fetchStatus = useCallback(async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      const response = await fetch('/api/jobs/summary');
      if (response.ok) {
        const data = await response.json();
        setStatus(data);
      }
    } catch (error) {
      console.error('Failed to fetch status:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStatus();
    const interval = setInterval(fetchStatus, refreshInterval);
    return () => clearInterval(interval);
  }, [fetchStatus, refreshInterval]);

  return (
    <Card>
      <BlockStack gap="300">
        <InlineStack align="space-between">
          <Text variant="headingSm" as="h4">
            {t('status')}
          </Text>
          {isLoading && <Spinner size="small" />}
        </InlineStack>

        <InlineStack gap="300" wrap>
          <div className="compact-status-item">
            <Text variant="bodyMd" fontWeight="semibold">
              {status.running}
            </Text>
            <Text variant="bodySm" tone="subdued">
              {t('running')}
            </Text>
          </div>
          <div className="compact-status-item">
            <Text variant="bodyMd" fontWeight="semibold">
              {status.completed}
            </Text>
            <Text variant="bodySm" tone="subdued">
              {t('completed')}
            </Text>
          </div>
          <div className="compact-status-item">
            <Text variant="bodyMd" fontWeight="semibold">
              {status.failed}
            </Text>
            <Text variant="bodySm" tone="subdued">
              {t('failed')}
            </Text>
          </div>
        </InlineStack>

        <Badge tone={status.systemStatus === 'operational' ? 'success' : 'warning'}>
          {t(status.systemStatus)}
        </Badge>
      </BlockStack>
    </Card>
  );
});

RealTimeStatus.displayName = 'RealTimeStatus';
CompactStatusWidget.displayName = 'CompactStatusWidget';

export default RealTimeStatus;
