import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Layout,
  TextField,
  Button,
  DataTable,
  Text,
  Badge,
  Card,
  EmptyState,
  BlockStack,
  Grid,
} from "@shopify/polaris";
import "../css/home.css";
import "../css/enhanced-cards.css";
import "../css/notifications.css";
import "../css/loading-states.css";
import { useTranslation } from "react-i18next";
import imageUrls from "../config/images";
import { useActionData, useSubmit, useNavigate } from "@remix-run/react";
import i18n from "i18next";
import { json } from "@remix-run/node";
import { loggerError, sendResponse } from "../utilities/helper";
import { authenticate } from "../config/shopify.js";
import { trackButtonClick, trackPageView } from "../utilities/mixpanel.js";
import { useApiCache } from "../hooks/useApiCache.js";
import React, { useState, useCallback, useEffect, useMemo, memo } from "react";
import { getHomeData } from "../models/home.server.js";
import { showPromptFlow } from "../config/config.js";
import { SpinnerComponent } from "../components/SpinnerComponent.jsx";
import { TemplateCard, EnhancedCard } from "../components/ui/EnhancedCard.jsx";
import { EnhancedButton } from "../components/ui/EnhancedButton.jsx";
import { ProfessionalNotificationProvider, useProfessionalNotifications } from "../components/ui/ProfessionalNotifications.jsx";
import ProfessionalDashboard from "../components/dashboard/ProfessionalDashboard.jsx";
import moment from "moment";
export async function action({ request }) {
  const { session } = await authenticate.admin(request);
  let bodyData;
  try {
    const data = { ...Object.fromEntries(await request.formData()) };
    data.store = session.shop;
    const homeData = await getHomeData(data);
    const actions = {
      initialFetch: async () => {
        bodyData = {
          status: "ok",
          functionType: "initialFetch",
          data: {
            shopName: session.shop,
            homeData: homeData.status === 200 ? homeData.data : null,
          },
        };
        return bodyData;
      },
    };
    const actionFn = actions[data.functionType];
    if (!actionFn) {
      throw new Error("Unknown functionType");
    }
    bodyData = await actionFn();
    return json(
      await sendResponse({
        status: bodyData.status || "ok",
        message: bodyData.message,
        data: bodyData,
      }),
    );
  } catch (error) {
    loggerError(`Error from fetch team ${error.message}`);
    return json(
      await sendResponse({
        status: "error",
        message: i18n.t("something_went_wrong"),
      }),
    );
  }
}

// Memoized Template Card Component - Enhanced but keeping original structure
const EnhancedTemplateCard = memo(({ template, onTemplateClick, isLoading }) => (
  <div
    className="clickable-box enhanced-template-card"
    onClick={() => onTemplateClick(template?.uuid)}
  >
    <Card>
      <BlockStack gap="100">
        <div className="emoji">{template.emoji}</div>
        <Text variant="headingMd" as="h4" truncate={true}>
          {template.title}
        </Text>
        <div className="truncate-2-lines">
          {template.description}
        </div>
      </BlockStack>
    </Card>
  </div>
));

// Memoized Task Input Section with enhanced styling
const TaskInputSection = memo(({
  taskInput,
  error,
  onTaskInputChange,
  onSubmit,
  t
}) => (
  <div className="enhanced-main-card">
    <Card>
      <BlockStack gap="4">
        <Text variant="headingLg" as="h2">
          {t("what_do_you_want_to_get_done_today")}
        </Text>
        <Text variant="bodyMd" as="p">
          {t("what_do_you_want_to_get_done_today_desc")}
        </Text>
        <br />
        <TextField
          value={taskInput}
          onChange={onTaskInputChange}
          multiline={2}
          autoComplete="off"
          error={error}
          placeholder={t("what_do_you_want_to_get_done_today_placeholder")}
        />
        <br />
        <div className="submit-button-container">
          <EnhancedButton
            variant="primary"
            size="medium"
            onClick={onSubmit}
          >
            {t("proceed")}
          </EnhancedButton>
        </div>
      </BlockStack>
    </Card>
  </div>
));

// Enhanced Home Page with Professional Dashboard
const HomePageContent = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const notifications = useProfessionalNotifications();
  const [isLoading, setIsLoading] = useState(false);
  const actionData = useActionData();
  const [shopName, setShopName] = useState("");
  const [taskInput, setTaskInput] = useState("");
  const [error, setError] = useState("");
  const [templates, setTemplates] = useState([]);
  const [workers, setWorkers] = useState([]);
  const [finalResultList, setFinalResultList] = useState([]);
  const submit = useSubmit();
  const [showBlur, setShowBlur] = useState(false);

  useEffect(() => {
    const hasVisited = localStorage.getItem("hasVisited");
    if (!hasVisited) {
      setShowBlur(true);
      localStorage.setItem("hasVisited", "true");
    }
  }, []);
  const initialFetch = useCallback(() => {
    setIsLoading(true);
    const result = {
      functionType: "initialFetch",
    };
    submit(result, { method: "post" });
  }, [submit]);

  useEffect(() => {
    initialFetch();
  }, [initialFetch]);

  // Memoized data processing
  const processedData = useMemo(() => {
    if (!actionData?.data?.data?.homeData) return null;

    const data = actionData.data.data;
    const homeData = data.homeData;

    const workersList = homeData.workersList?.slice(0, 3) || [];
    const finalResultList = homeData.finalResultList?.slice(0, 3) || [];

    const resultList = finalResultList.map((done) => [
      done?.title,
      t(done?.status) || "-",
      done?.created_at || "-",
    ]);

    const workers = workersList.map((worker, index) => [
      worker?.job_title || `${t("job")} ${index + 1}`,
      t(worker?.job_status) || "-",
      moment(worker.created_at).format("MMM DD, YYYY h:mm A") || "-",
    ]);

    return {
      templates: homeData.templateList || [],
      workers,
      resultList,
      shopName: data.shopName
    };
  }, [actionData, t]);

  useEffect(() => {
    if (actionData) {
      if (actionData.status === "ok" && processedData) {
        setTemplates(processedData.templates);
        setWorkers(processedData.workers);
        setFinalResultList(processedData.resultList);
        setShopName(processedData.shopName);
      } else if (actionData.status !== "ok") {
        // Handle error case
        console.error("Action data error:", actionData.message);
      }
      setIsLoading(false);
    }
  }, [actionData, processedData]);

  useEffect(() => {
    if (shopName) {
      trackPageView("Home", { shop_name: shopName });
    }
  }, [shopName]);

  const handleTaskInputChange = useCallback((value) => {
    setTaskInput(value);
    setError("");
  }, []);

  const handleSubmit = useCallback(() => {
    if (!taskInput || taskInput.trim() === "") {
      setError(t("please_do_let_us_know_your_requirement"));
      return;
    }
    trackButtonClick("Create Workers flow", "Home", {
      shop_name: shopName,
    });
    navigate(`/app/create/workers`, { state: { input: taskInput } });
  }, [taskInput, t, shopName, navigate]);

  const handleTemplateClick = useCallback((templateUuid) => {
    setIsLoading(true);
    notifications.info(t('loading_template') || 'Loading template...');
    navigate(`/app/template/${templateUuid}`);
  }, [navigate, notifications, t]);

  return (
    <Page title={t("home_page_title")} subtitle={t("home_page_desc")}>
      {isLoading && <SpinnerComponent />}
      <Layout>
        {/* Professional Dashboard */}
        <Layout.Section>
          <ProfessionalDashboard
            shopName={shopName}
            templates={templates}
            workers={workers}
            loading={isLoading}
          />
        </Layout.Section>

        <div style={{ width: "100%" }}>
          <Layout.Section>
            {showPromptFlow && (
              <TaskInputSection
                taskInput={taskInput}
                error={error}
                onTaskInputChange={handleTaskInputChange}
                onSubmit={handleSubmit}
                t={t}
              />
            )}
          </Layout.Section>
        </div>
        <div
          className={
            showPromptFlow && showBlur
              ? "blurred-container"
              : "full-width-container"
          }
        >
          <Layout.Section>
            <div className="enhanced-main-card">
              <Card>
                <BlockStack gap="4">
                  <Text variant="headingMd" as="h3">
                    {t("popular_store_workers")}
                  </Text>
                  <Text variant="bodyMd" as="p">
                    {t("popular_store_workers_desc")}
                  </Text>
                  <div
                    className={
                      templates.length === 0 ? null : "templates-container"
                    }
                  >
                    {templates.length === 0 ? (
                      <EmptyState
                        heading={t("no_templates_available")}
                        //action={{ content: 'Add content', url: '/app/contents/new' }}
                        image={imageUrls.emptyImage}
                      >
                        <p>{t("you_do_not_have_any_templates_right_now")}</p>
                      </EmptyState>
                    ) : (
                      <div className="task-selection">
                        {templates.map((template, index) => (
                          <EnhancedTemplateCard
                            key={template?.uuid || index}
                            template={template}
                            onTemplateClick={handleTemplateClick}
                            isLoading={isLoading}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                </BlockStack>
              </Card>
            </div>
          </Layout.Section>

          <Layout.Section>
            <div className="enhanced-main-card">
              <Card>
                <BlockStack gap="4">
                  <Text variant="headingMd" as="h3">
                    {t("recently_completed_jobs")}
                  </Text>
                  {workers.length === 0 ? (
                    <div style={{ padding: "1rem", textAlign: "center" }}>
                      <EmptyState
                        heading={t("no_jobs_created_yet")}
                        image={imageUrls.emptyImage}
                      />
                    </div>
                  ) : (
                    <DataTable
                      columnContentTypes={["text", "text", "text"]}
                      headings={[t("job_name"), t("status"), t("date")]}
                      rows={workers}
                    />
                  )}
                </BlockStack>
              </Card>
            </div>
          </Layout.Section>
        </div>
      </Layout>
      <br />
    </Page>
  );
};

// Main component wrapped with professional notification provider
export default function HomePage() {
  return (
    <ProfessionalNotificationProvider>
      <HomePageContent />
    </ProfessionalNotificationProvider>
  );
}
