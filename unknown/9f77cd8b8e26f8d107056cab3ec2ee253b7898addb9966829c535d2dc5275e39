/* Professional Design System - Utility Classes */

/* === SPACING UTILITIES === */
.space-xs { gap: var(--space-xs); }
.space-sm { gap: var(--space-sm); }
.space-md { gap: var(--space-md); }
.space-lg { gap: var(--space-lg); }
.space-xl { gap: var(--space-xl); }
.space-2xl { gap: var(--space-2xl); }
.space-3xl { gap: var(--space-3xl); }

.p-xs { padding: var(--space-xs); }
.p-sm { padding: var(--space-sm); }
.p-md { padding: var(--space-md); }
.p-lg { padding: var(--space-lg); }
.p-xl { padding: var(--space-xl); }
.p-2xl { padding: var(--space-2xl); }

.m-xs { margin: var(--space-xs); }
.m-sm { margin: var(--space-sm); }
.m-md { margin: var(--space-md); }
.m-lg { margin: var(--space-lg); }
.m-xl { margin: var(--space-xl); }
.m-2xl { margin: var(--space-2xl); }

/* === TYPOGRAPHY UTILITIES === */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }

.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.leading-tight { line-height: var(--line-height-tight); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }

/* === COLOR UTILITIES === */
.text-primary { color: var(--color-text-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-tertiary { color: var(--color-text-tertiary); }
.text-success { color: var(--color-success-600); }
.text-warning { color: var(--color-warning-600); }
.text-error { color: var(--color-error-600); }

.bg-surface { background-color: var(--color-surface); }
.bg-surface-hover { background-color: var(--color-surface-hover); }
.bg-secondary { background-color: var(--color-background-secondary); }
.bg-tertiary { background-color: var(--color-background-tertiary); }

/* === BORDER UTILITIES === */
.border { border: 1px solid var(--color-border); }
.border-hover { border-color: var(--color-border-hover); }
.border-primary { border-color: var(--color-primary-500); }
.border-success { border-color: var(--color-success-500); }
.border-warning { border-color: var(--color-warning-500); }
.border-error { border-color: var(--color-error-500); }

.rounded-xs { border-radius: var(--radius-xs); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

/* === SHADOW UTILITIES === */
.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }

/* === TRANSITION UTILITIES === */
.transition-fast { transition: all var(--transition-fast); }
.transition-normal { transition: all var(--transition-normal); }
.transition-slow { transition: all var(--transition-slow); }

/* === LAYOUT UTILITIES === */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

/* === INTERACTIVE STATES === */
.interactive {
  cursor: pointer;
  transition: var(--transition-fast);
}

.interactive:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.interactive:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* === FOCUS STATES === */
.focus-ring:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.focus-ring:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* === ACCESSIBILITY === */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* === RESPONSIVE UTILITIES === */
@media (max-width: 768px) {
  .mobile-hidden { display: none; }
  .mobile-full { width: 100%; }
}

@media (min-width: 769px) {
  .desktop-hidden { display: none; }
}

/* === ANIMATION KEYFRAMES === */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(10px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-fade-in {
  animation: fadeIn var(--transition-normal);
}

.animate-slide-up {
  animation: slideUp var(--transition-normal);
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* === HOVER EFFECTS === */
.hover-lift:hover {
  transform: translateY(-2px);
  transition: var(--transition-fast);
}

.hover-scale:hover {
  transform: scale(1.02);
  transition: var(--transition-fast);
}

.hover-shadow:hover {
  box-shadow: var(--shadow-lg);
  transition: var(--transition-fast);
}

/* === LOADING STATES === */
.loading-skeleton {
  background: linear-gradient(90deg, 
    var(--color-neutral-200) 25%, 
    var(--color-neutral-100) 50%, 
    var(--color-neutral-200) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* === PROFESSIONAL GRADIENTS === */
.gradient-primary {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
}

.gradient-success {
  background: linear-gradient(135deg, var(--color-success-500), var(--color-success-600));
}

.gradient-subtle {
  background: linear-gradient(135deg, var(--color-neutral-50), var(--color-neutral-100));
}
