import React, { memo } from "react";
import { Button } from "@shopify/polaris";

/**
 * Enhanced Button Component with professional styling and variants
 */
export const EnhancedButton = memo(({
  children,
  variant = "primary",
  size = "medium",
  loading = false,
  disabled = false,
  icon,
  iconPosition = "left",
  fullWidth = false,
  className = "",
  onClick,
  ...props
}) => {
  const getButtonClasses = () => {
    let classes = "enhanced-button";
    
    // Add variant classes
    classes += ` enhanced-button--${variant}`;
    
    // Add size classes
    classes += ` enhanced-button--${size}`;
    
    // Add state classes
    if (loading) classes += " enhanced-button--loading";
    if (disabled) classes += " enhanced-button--disabled";
    if (fullWidth) classes += " enhanced-button--full-width";
    if (icon) classes += " enhanced-button--with-icon";
    
    return `${classes} ${className}`;
  };

  const handleClick = (e) => {
    if (onClick && !loading && !disabled) {
      onClick(e);
    }
  };

  // Map our variants to Polaris variants
  const getPolarisVariant = () => {
    switch (variant) {
      case "primary": return "primary";
      case "secondary": return "secondary";
      case "tertiary": return "tertiary";
      case "destructive": return "primary";
      case "success": return "primary";
      case "warning": return "primary";
      default: return "secondary";
    }
  };

  return (
    <div className={getButtonClasses()}>
      <Button
        variant={getPolarisVariant()}
        size={size}
        loading={loading}
        disabled={disabled}
        fullWidth={fullWidth}
        onClick={handleClick}
        {...props}
      >
        {icon && iconPosition === "left" && (
          <span className="enhanced-button__icon enhanced-button__icon--left">
            {icon}
          </span>
        )}
        <span className="enhanced-button__text">{children}</span>
        {icon && iconPosition === "right" && (
          <span className="enhanced-button__icon enhanced-button__icon--right">
            {icon}
          </span>
        )}
      </Button>
    </div>
  );
});

/**
 * Icon Button Component - For icon-only buttons
 */
export const IconButton = memo(({
  icon,
  tooltip,
  variant = "tertiary",
  size = "medium",
  loading = false,
  disabled = false,
  className = "",
  onClick,
  ...props
}) => {
  return (
    <div className={`icon-button icon-button--${variant} icon-button--${size} ${className}`}>
      <Button
        variant={variant === "primary" ? "primary" : "tertiary"}
        size={size}
        loading={loading}
        disabled={disabled}
        onClick={onClick}
        accessibilityLabel={tooltip}
        {...props}
      >
        {icon}
      </Button>
    </div>
  );
});

/**
 * Button Group Component - For grouping related buttons
 */
export const ButtonGroup = memo(({
  children,
  spacing = "sm",
  alignment = "left",
  className = ""
}) => {
  return (
    <div className={`button-group button-group--${spacing} button-group--${alignment} ${className}`}>
      {children}
    </div>
  );
});

/**
 * Floating Action Button Component
 */
export const FloatingActionButton = memo(({
  icon,
  onClick,
  variant = "primary",
  size = "large",
  position = "bottom-right",
  className = ""
}) => {
  return (
    <div className={`fab fab--${variant} fab--${size} fab--${position} ${className}`}>
      <button
        className="fab__button"
        onClick={onClick}
        aria-label="Floating action button"
      >
        {icon}
      </button>
    </div>
  );
});

/**
 * Split Button Component - Button with dropdown
 */
export const SplitButton = memo(({
  children,
  actions = [],
  variant = "primary",
  size = "medium",
  loading = false,
  disabled = false,
  onPrimaryAction,
  className = ""
}) => {
  return (
    <div className={`split-button split-button--${variant} split-button--${size} ${className}`}>
      <EnhancedButton
        variant={variant}
        size={size}
        loading={loading}
        disabled={disabled}
        onClick={onPrimaryAction}
        className="split-button__primary"
      >
        {children}
      </EnhancedButton>
      <div className="split-button__dropdown">
        <IconButton
          icon="▼"
          variant={variant}
          size={size}
          disabled={disabled}
          className="split-button__dropdown-trigger"
        />
        {/* Dropdown menu would be implemented here */}
      </div>
    </div>
  );
});

EnhancedButton.displayName = 'EnhancedButton';
IconButton.displayName = 'IconButton';
ButtonGroup.displayName = 'ButtonGroup';
FloatingActionButton.displayName = 'FloatingActionButton';
SplitButton.displayName = 'SplitButton';
